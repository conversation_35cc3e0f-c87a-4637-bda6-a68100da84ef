/// <reference types="cypress" />

// Mock data for member API
const memberMockData = {
  id: 'mem_1',
  email: '<EMAIL>',
  lwa_accounts: [
    {
      account_id: 'acc_1',
      account_type: 'seller',
      seller_id: 'SELLER123',
      marketplaces: [
        {
          id: 'mk_seller_us',
          marketplace_id: 'ATVPDKIKX0DER',
          marketplace_name: 'United States',
          country_code: 'US',
          default_currency_code: 'USD',
          subscription_yn: 'Y',
          ad_lwa_validation_yn: 'Y',
          sp_lwa_validation_yn: 'Y',
          subscription_features: { number_of_optimization_sets: 5 }
        }
      ]
    }
  ]
};

// Mock data for optimization sets
const optimizationSetsMockData = [
  {
    id: 98,
    account_id: 'ENTITYPHJJN8ZVVUKS',
    marketplace_id: 'A2EUQ1WTGCTBG2',
    optimization_name: 'Test Optimization Set',
    ad_budget_amount: 14112,
    target_products: [
      {
        asin: 'B08N5WRWNW',
        campaigns: [
          {
            campaign_id: 'campaign_1',
            ad_groups: [
              {
                ad_group_id: 'adgroup_1',
                ad_group_name: 'Test Ad Group'
              }
            ]
          }
        ]
      }
    ]
  }
];

// Mock data for targets
const targetsMockData = [
  {
    target_id: 'target_1',
    target_type: 'KEYWORD',
    keyword: 'test keyword',
    bid: 1.50,
    state: 'ENABLED',
    creation_date_time: '2024-01-01T00:00:00Z'
  },
  {
    target_id: 'target_2',
    target_type: 'PRODUCT',
    asin: 'B08N5WRWNW',
    bid: 2.00,
    state: 'PAUSED',
    creation_date_time: '2024-01-01T00:00:00Z'
  }
];

// Success response for adding target
const addTargetSuccessResponse = {
  status: true,
  message: 'Target added successfully',
  target_id: 'new_target_123'
};

// Error response for adding target
const addTargetErrorResponse = {
  status: false,
  message: 'Failed to add target',
  error: 'Invalid target data'
};

// Validation test data
export const validationTestData = {
  validKeyword: {
    keyword: 'test keyword',
    bid: '1.50',
    matchType: 'Exact'
  },
  validProduct: {
    asin: 'B08N5WRWNW',
    bid: '2.00'
  },
  invalidKeyword: {
    tooShort: 'a',
    empty: ''
  },
  invalidBid: {
    negative: '-1.00',
    zero: '0.00',
    empty: ''
  },
  invalidAsin: {
    tooShort: 'B123',
    invalidFormat: 'invalid-asin',
    empty: ''
  }
};

// Main mock setup function for AddTargetModal
export const setupAddTargetModalMocks = () => {
  // Mock member API
  cy.intercept('GET', '/api/member', {
    statusCode: 200,
    body: memberMockData
  }).as('getMember');

  // Mock optimization sets API
  cy.intercept('GET', '/api/optimization/list_optimizations*', {
    statusCode: 200,
    body: optimizationSetsMockData
  }).as('getListOptimizations');

  // Mock targets API
  cy.intercept('GET', '/api/optimization/list_targets*', {
    statusCode: 200,
    body: targetsMockData
  }).as('getTargets');

  // Mock add target API (success)
  cy.intercept('POST', '/api/optimization/targets', {
    statusCode: 200,
    body: addTargetSuccessResponse
  }).as('addTarget');

  // Mock other required APIs
  cy.intercept('GET', '/api/member/available_profiles*', {
    statusCode: 200,
    body: []
  }).as('getAvailableProfiles');

  cy.intercept('GET', '/api/hourlyReport/new_budget_pacing*', {
    statusCode: 200,
    body: {
      daily_spending_history: {},
      total_budget_usage: 0
    }
  }).as('getNewBudgetPacing');

  cy.intercept('GET', '/api/hourlyReport/campaign-budget-usage*', {
    statusCode: 200,
    body: {
      asins: [
        {
          asin: 'B08N5WRWNW',
          campaigns: [
            {
              campaign_id: 'campaign_1',
              total_budget_usage: 150.50,
              today_budget_usage: 25.75
            }
          ]
        }
      ]
    }
  }).as('getCampaignBudgetUsage');

  cy.intercept('GET', '/api/optimization/list_target_candidate_asins*', {
    statusCode: 200,
    body: []
  }).as('getListTargetCandidateAsins');
};

// Error scenarios setup
export const setupAddTargetModalErrorMocks = () => {
  // Mock add target error
  cy.intercept('POST', '/api/optimization/targets', {
    statusCode: 400,
    body: addTargetErrorResponse
  }).as('addTargetError');
};

// Slow API mock for performance testing
export const setupSlowAddTargetMock = () => {
  cy.intercept('POST', '/api/optimization/targets', (req) => {
    req.reply((res) => {
      res.delay(2000);
      res.send({ status: true });
    });
  }).as('slowAddTarget');
};

// Selectors for AddTargetModal components
export const addTargetModalSelectors = {
  // Navigation selectors
  optimizationSetItem: '.divide-y > li',
  productItem: '[data-testid="product-item"]',
  adGroupItem: '[data-testid="ad-group-item"]',
  addTargetButton: 'button.bg-blue-100:has(.h-5.w-5)', // Button with PlusIcon
  
  // Modal selectors
  modal: {
    container: '[role="dialog"]',
    title: '[role="dialog"] h3',
    closeButton: '[role="dialog"] button[aria-label="Close"]',
    cancelButton: 'button:contains("Cancel")',
    addButton: 'button:contains("Add")'
  },
  
  // Form selectors
  form: {
    keywordInput: 'input[placeholder="Enter keyword"]',
    asinInput: 'input[placeholder*="Enter ASIN"]',
    bidInput: 'input[placeholder="Enter bid amount"]',
    matchTypeButton: 'button[role="combobox"]',
    matchTypeDropdown: '[role="listbox"]'
  },
  
  // Error and loading selectors
  errors: {
    fieldError: '.text-red-600',
    apiError: '.text-red-600'
  },
  loading: {
    spinner: '.MuiCircularProgress-root'
  },
  
  // Switch components (for integration testing)
  switch: {
    container: '.MuiSwitch-root',
    input: '.MuiSwitch-root input[type="checkbox"]',
    targetListItem: 'li.p-4'
  }
};
