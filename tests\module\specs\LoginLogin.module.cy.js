import LoginLoginPage from "../../pages/LoginLoginPage";

const page = new LoginLoginPage();

describe('Login', () => {
  it.guide('처음 접속하면, email/password 입력창이 있다.', {
    actionFunc: () => {
      cy.visit('/');
      page.waitForLoad();
    },
    assertFunc: () => {
      page.assertUsername();
      page.assertPassword();
    },
  });

  it.guide('부정확한 email이나 password를 입력하고 "Sign In" 버튼을 클릭하면, 에러 메시지를 보여준다.', {
    // TODO: cross origin 문제 해결필요
    actionFunc: () => {
      cy.visit('/');
      page.waitForLoad();

      // cy.visit('https://global-mop-studio-dev.auth.us-east-1.amazoncognito.com/login?response_type=code&client_id=5i114n09ft571rb9mrggmk8bfo&redirect_uri=https%3A%2F%2Fstudio.dev.optapex.com%2Fapi%2Fauth%2Fcallback%2Fcognito&code_challenge=0g_XqzwD1EOZActCkhO3hgOJ5cWuViCv9V__zJT78z8&code_challenge_method=S256&scope=openid+profile+email');
      page.inputUsername('testtest');
      page.inputPassword('213512345');
      page.clickSubmitButton();
    },
    assertFunc: () => {
      page.assertErrorMessage();
    },
  });

  it.guide('유효한 email/password를 입력하고 "Sign In" 버튼을 클릭하면, 성공적으로 로그인이 된다.', {
    actionFunc: () => {
      cy.login();
      cy.visit('/');
    },
    assertFunc: () => {
      cy.url().should('include', 'dashboard');
    },
  });

  it.guide('Forgot your password를 누르면 비밀번호 초기화할 이메일을 입력할 수 있는 페이지로 이동한다.',{
    actionFunc: () =>{
      cy.visit('/');
      page.waitForLoad();
      page.clickForgotPassword();
    },
    assertFunc:()=>{
      cy.url().should('include', 'forgotPassword');
    }
  })

  xit.guide('Create an account를 누르면 회원가입 할 수 있는 페이지로 이동한다.',{
    actionFunc: () =>{
      cy.visit('/');
      page.waitForLoad();
      page.clickCreateAccount();
    },
    assertFunc:()=>{
      cy.url().should('include', 'signup');
    }
  })

});
