/// <reference types="cypress" />

// Mock data for OptimizationSets with Switch components
export const optimizationSetsMockData = [
  {
    id: 98,
    account_id: 'ENTITYPHJJN8ZVVUKS',
    marketplace_id: 'A2EUQ1WTGCTBG2',
    optimization_name: 'LGE Canada Opt Set 1',
    ad_budget_type: 'DATERANGE',
    ad_budget_amount: 14112,
    ad_budget_start_date: '2024-10-04',
    ad_budget_end_date: '2024-12-31',
    optimization_range: 'SPPLUS',
    optimization_goal: 'SALES',
    optimization_option: 'NONE',
    optimization_target_type: 'NONE',
    optimization_target_value: 0,
    bid_yn: 'Y',
    use_yn: 'Y',
    created_by: 4,
    creation_datetime: '2024-10-15T16:43:06',
    updated_by: 4,
    last_update_datetime: '2024-12-10T05:21:03',
    request_status: 'DONE',
    portfolio_id: '**************',
    account_type: 'seller',
    selling_partner_id: 'SELLER123',
    display_yn: 'Y',
    target_products: [
      {
        asin: 'B08N5WRWNW',
        product_name: 'Wireless Headphones',
        sku: 'WH-001',
        campaigns: [
          {
            campaign_id: 'campaign_1',
            campaign_name: 'Test Campaign 1',
            campaign_state: 'ENABLED',
            campaign_targeting_settings: 'MANUAL',
            campaign_start_date: '2024-01-01',
            campaign_mop_yn: 'Y',
            ad_groups: [
              {
                ad_group_id: 'adgroup_1',
                ad_group_name: 'Test Ad Group 1',
                ad_group_state: 'ENABLED',
                ad_group_type: 'KEYWORD',
                ad_group_creation_date_time: '2024-01-01T00:00:00Z',
              },
              {
                ad_group_id: 'adgroup_2',
                ad_group_name: 'Test Ad Group 2',
                ad_group_state: 'PAUSED',
                ad_group_type: 'PRODUCT',
                ad_group_creation_date_time: '2024-01-02T00:00:00Z',
              },
            ],
          },
        ],
      },
      {
        asin: 'B07XJ8C8F5',
        product_name: 'Bluetooth Speaker',
        sku: 'BS-002',
        campaigns: [
          {
            campaign_id: 'campaign_2',
            campaign_name: 'Test Campaign 2',
            campaign_state: 'ENABLED',
            campaign_targeting_settings: 'AUTO',
            campaign_start_date: '2024-01-05',
            campaign_mop_yn: 'Y',
            ad_groups: [
              {
                ad_group_id: 'adgroup_3',
                ad_group_name: 'Test Ad Group 3',
                ad_group_state: 'ENABLED',
                ad_group_type: 'KEYWORD',
                ad_group_creation_date_time: '2024-01-05T00:00:00Z',
              },
            ],
          },
        ],
      },
    ],
  },
];

export const targetsMockData = [
  {
    target_id: 'target_1',
    target: 'wireless headphones',
    target_type: 'KEYWORD',
    match_type: 'EXACT',
    state: 'ENABLED',
    negative: false,
    bid: 1.5,
    campaign_id: 'campaign_1',
    ad_group_id: 'adgroup_1',
    creation_date_time: '2024-01-01T00:00:00Z',
  },
  {
    target_id: 'target_2',
    target: 'B08N5WRWNW',
    target_type: 'PRODUCT',
    state: 'PAUSED',
    negative: false,
    bid: 2.0,
    campaign_id: 'campaign_1',
    ad_group_id: 'adgroup_1',
    creation_date_time: '2024-01-02T00:00:00Z',
  },
  {
    target_id: 'target_3',
    target: 'bluetooth speakers',
    target_type: 'KEYWORD',
    match_type: 'PHRASE',
    state: 'ENABLED',
    negative: false,
    bid: 1.25,
    campaign_id: 'campaign_1',
    ad_group_id: 'adgroup_1',
    creation_date_time: '2024-01-03T00:00:00Z',
  },
  {
    target_id: 'target_4',
    target: 'cheap headphones',
    target_type: 'KEYWORD',
    match_type: 'EXACT',
    state: 'PAUSED',
    negative: true,
    campaign_id: 'campaign_1',
    ad_group_id: 'adgroup_1',
    creation_date_time: '2024-01-04T00:00:00Z',
  },
  {
    target_id: 'target_5',
    target: 'B07XJ8C8F5',
    target_type: 'PRODUCT',
    state: 'ENABLED',
    negative: true,
    campaign_id: 'campaign_1',
    ad_group_id: 'adgroup_1',
    creation_date_time: '2024-01-05T00:00:00Z',
  },
];

export const memberMockData = {
  id: 'mem_1',
  email: '<EMAIL>',
  lwa_accounts: [
    {
      account_id: 'ENTITYPHJJN8ZVVUKS',
      account_type: 'seller',
      seller_id: 'SELLER123',
      marketplaces: [
        {
          id: 'mk_seller_ca',
          marketplace_id: 'A2EUQ1WTGCTBG2',
          marketplace_name: 'Canada',
          country_code: 'CA',
          default_currency_code: 'CAD',
          subscription_yn: 'Y',
          ad_lwa_validation_yn: 'Y',
          sp_lwa_validation_yn: 'Y',
          subscription_features: { number_of_optimization_sets: 5 },
        },
      ],
    },
  ],
};

// API Response mocks
export const changeTargetStatusSuccessResponse = {
  status: true,
  message: 'Target status updated successfully',
};

export const changeTargetStatusErrorResponse = {
  status: false,
  message: 'Failed to update target status',
  detail: 'Target not found or server error',
};

export const addTargetSuccessResponse = {
  status: true,
  message: 'Target added successfully',
};

export const addTargetErrorResponse = {
  status: false,
  message: 'Failed to add target',
  detail: 'Invalid target data provided',
};

// Setup functions for mocking APIs
export const setupOptimizationSetsSwitchMocks = () => {
  // Mock member API
  cy.intercept('GET', '/api/member', {
    statusCode: 200,
    body: memberMockData,
  }).as('getMember');

  // Mock optimization sets API
  cy.intercept('GET', '/api/optimization/list_optimizations*', {
    statusCode: 200,
    body: optimizationSetsMockData,
  }).as('getListOptimizations');

  // Mock targets API
  cy.intercept('GET', '/api/optimization/list_targets*', {
    statusCode: 200,
    body: targetsMockData,
  }).as('getTargets');

  // Mock change target status API
  cy.intercept('PUT', '/api/optimization/change_status_of_target*', {
    statusCode: 200,
    body: changeTargetStatusSuccessResponse,
  }).as('changeTargetStatus');

  // Mock add target API
  cy.intercept('POST', '/api/optimization/targets', {
    statusCode: 200,
    body: addTargetSuccessResponse,
  }).as('addTarget');

  // Mock other required APIs
  cy.intercept('GET', '/api/member/available_profiles*', {
    statusCode: 200,
    body: [],
  }).as('getAvailableProfiles');

  cy.intercept('GET', '/api/hourlyReport/new_budget_pacing*', {
    statusCode: 200,
    body: {
      daily_spending_history: {},
      total_budget_usage: 0,
    },
  }).as('getNewBudgetPacing');

  cy.intercept('GET', '/api/hourlyReport/campaign-budget-usage*', {
    statusCode: 200,
    body: {
      asins: [
        {
          asin: 'B08N5WRWNW',
          campaigns: [
            {
              campaign_id: 'campaign_1',
              total_budget_usage: 150.5,
              today_budget_usage: 25.75,
            },
          ],
        },
      ],
    },
  }).as('getCampaignBudgetUsage');

  cy.intercept('GET', '/api/optimization/list_target_candidate_asins*', {
    statusCode: 200,
    body: [],
  }).as('getListTargetCandidateAsins');
};

// Error scenarios setup
export const setupOptimizationSetsSwitchErrorMocks = () => {
  // Mock change target status error
  cy.intercept('PUT', '/api/optimization/change_status_of_target*', {
    statusCode: 500,
    body: changeTargetStatusErrorResponse,
  }).as('changeTargetStatusError');

  // Mock add target error
  cy.intercept('POST', '/api/optimization/targets', {
    statusCode: 400,
    body: addTargetErrorResponse,
  }).as('addTargetError');
};

// Validation test data for AddTargetModal
export const validationTestData = {
  validKeyword: {
    keyword: 'test keyword',
    bid: '1.50',
    matchType: 'Exact',
  },
  validProduct: {
    asin: 'B08N5WRWNW',
    bid: '2.00',
  },
  invalidKeyword: {
    tooShort: 'a',
    empty: '',
  },
  invalidBid: {
    negative: '-1.00',
    zero: '0.00',
    empty: '',
  },
  invalidAsin: {
    tooShort: 'B123',
    invalidFormat: 'invalid-asin',
    empty: '',
  },
};

export default {
  optimizationSetsMockData,
  targetsMockData,
  memberMockData,
  setupOptimizationSetsSwitchMocks,
  setupOptimizationSetsSwitchErrorMocks,
  optimizationSetsSwitchSelectors,
  changeTargetStatusSuccessResponse,
  changeTargetStatusErrorResponse,
  addTargetSuccessResponse,
  addTargetErrorResponse,
};
