/// <reference types="cypress" />

import BasePage from './BasePage';

class LoginLoginPage extends BasePage {
    INPUT_USERNAME = 'input[name="username"]';
    INPUT_PASSWORD = 'input[name="password"]';
    BUTTON_SUBMIT = 'button[type="submit"]';
    ERROR_MESSAGE = 'span[class*="awsui_error__message_"]';
    FORGOT_PASSWORD = 'a[href*="forgotPassword"]';
    CREATE_ACCOUNT = 'a[href*="signup"]';

    waitForLoad(){
        cy.get(this.INPUT_USERNAME, { timeout: 30000 }).should('be.visible');
        cy.get(this.INPUT_PASSWORD, { timeout: 30000 }).should('be.visible');
        cy.get(this.BUTTON_SUBMIT, { timeout: 30000 }).should('be.visible');
    }

    assertUsername(){
        cy.get(this.INPUT_USERNAME).should('exist').and('be.visible');
    }

    assertPassword(){
        cy.get(this.INPUT_PASSWORD).should('exist').and('be.visible');
    }

    assertSubmitButton(){
        cy.get(this.BUTTON_SUBMIT).should('exist').and('be.visible');
    }

    assertErrorMessage(){
        cy.get(this.ERROR_MESSAGE).should('exist').and('be.visible');
    }

    clickSubmitButton(){
        cy.get(this.BUTTON_SUBMIT).click({force:true});
    }

    clickForgotPassword(){
        cy.get(this.FORGOT_PASSWORD).click();
    }

    clickCreateAccount(){
        cy.get(this.CREATE_ACCOUNT).click();
    }

    inputUsername(username){
        cy.get(this.INPUT_USERNAME).type(username, {force:true});
    }

    inputPassword(password){
        cy.get(this.INPUT_PASSWORD).type(password);
    }

}

export default LoginLoginPage;