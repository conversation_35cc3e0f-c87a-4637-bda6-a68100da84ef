"use client"

import { useState } from "react"
import { useLocale } from 'next-intl';
import { cn } from "@/utils/msc"
import { Dialog, DialogBackdrop, DialogPanel, Transition, TransitionChild } from '@headlessui/react'
import { XMarkIcon, BookOpenIcon, QuestionMarkCircleIcon } from "@heroicons/react/20/solid"

export default function DocsButton() {
  const locale = useLocale();
  const [showDocsModal, setShowDocsModal] = useState(false);

  return (
    <>
      <button
        className={cn(
          "group relative flex items-center gap-x-2 px-2 cursor-pointer rounded-lg h-9 py-1.5 focus:outline-none text-xs overflow-hidden border border-transparent",
          "transition-width ease-in-out delay-150",
          showDocsModal ? "w-[165px] bg-blue-700" : "w-[165px]"
        )}
        onClick={() => setShowDocsModal(true)}
        >
        <div className="flex-shrink-0 border-2 border-yellow-300 group-hover:border-yellow-200 rounded-full p-[0.5px]">
          <QuestionMarkCircleIcon
            className={cn(
              "flex-shrink-0 h-4 w-4 text-yellow-300 group-hover:text-yellow-200",
              showDocsModal ? "text-yellow-100" : ""
            )}
            aria-hidden="true"
          />
        </div>
        <span
          className={cn(
            "flex-shrink-0 block text-gray-200 group-hover:text-gray-100 font-semibold",
            showDocsModal ? "text-white" : ""
          )}
        >
          Help & Support
        </span>
      </button>
      {showDocsModal && (
        <Transition appear show={showDocsModal}>
          <Dialog as="div" className="relative z-50 focus:outline-none" onClose={() => setShowDocsModal(false)}>
            <DialogBackdrop className="fixed inset-0 bg-black/30 backdrop-blur-sm" aria-hidden="true" />
            <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
              <div className="flex flex-col min-h-full items-center justify-center py-12 px-12">
                <TransitionChild
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 transform-[scale(95%)]"
                  enterTo="opacity-100 transform-[scale(100%)]"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 transform-[scale(100%)]"
                  leaveTo="opacity-0 transform-[scale(95%)]"
                >
                  <DialogPanel className="flex flex-col flex-1 w-full rounded-xl bg-white overflow-hidden shadow-lg">
                    <div className="flex-shrink-0 px-6 pt-4 text-right">
                      <button onClick={() => setShowDocsModal(false)}>
                        <XMarkIcon className="h-5 w-5 text-gray-500 hover:text-gray-800" />
                      </button>
                    </div>
                    <div className="mt-2 relative min-h-0 flex-1 size-full">
                      <div className="absolute inset-0 ">
                        <iframe
                          src={
                            locale === "en"
                              ? "https://agricultural-hoverfly.super.site/"
                              : "https://help-kr.optapex.com/"
                          }
                          title="Documentation"
                          className="block size-full border-none"
                        />
                      </div>
                    </div>
                  </DialogPanel>
                </TransitionChild>
              </div>
            </div>
          </Dialog>
        </Transition>
      )}
    </>
  );
}