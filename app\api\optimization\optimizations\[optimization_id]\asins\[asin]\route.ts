import { ErrorResponse } from "@/app/api/_models";
import { getServerApiHostUrl } from "@/utils/host";
import { NextRequest, NextResponse } from "next/server";

export type GetOptimizationAsinDetailResponse = any;

export async function GET(
  request: NextRequest,
  { params }: { params: { optimization_id: string, asin: string } }
): Promise<NextResponse<GetOptimizationAsinDetailResponse | ErrorResponse>> {
  const accessToken = request.headers.get("Authorization");
  if (!accessToken) {
    return NextResponse.json(
      { message: "Authorization header is missing" },
      { status: 400 }
    );
  }
  const accountId = request.nextUrl.searchParams.get("account_id")
  if (!accountId) {
    return NextResponse.json(
      { message: "account_id query is missing" },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get("marketplace_id")
  if (!marketplaceId) {
    return NextResponse.json(
      { message: "marketplace_id query is missing" },
      { status: 400 }
    );
  }

  const { optimization_id, asin } = params
  if (!optimization_id || !asin) {
    return NextResponse.json(
      { message: "optimization_id or asin param is missing" },
      { status: 400 }
    );
  }

  const resData = await fetch(
    `${await getServerApiHostUrl()}/api/optimization/optimizations/${optimization_id}/asins/${encodeURIComponent(asin)}?account_id=${accountId}&marketplace_id=${marketplaceId}`,
    {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-cache",
    }
  ).then((res) => res.json());

  return NextResponse.json(resData, { status: 200 });
} 