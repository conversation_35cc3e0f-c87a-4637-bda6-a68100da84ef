/// <reference types="cypress" />

// Simple mock data for OptimizationSets tests
const memberMockData = {
  id: 'mem_1',
  email: '<EMAIL>',
  lwa_accounts: [
    {
      account_id: 'acc_1',
      account_type: 'seller',
      seller_id: 'SELLER123',
      marketplaces: [
        {
          id: 'mk_seller_us',
          marketplace_id: 'ATVPDKIKX0DER',
          marketplace_name: 'United States',
          country_code: 'US',
          default_currency_code: 'USD',
          subscription_yn: 'Y',
          ad_lwa_validation_yn: 'Y',
          sp_lwa_validation_yn: 'Y',
          subscription_features: { number_of_optimization_sets: 5 }
        }
      ]
    }
  ]
};

const optimizationSetsMockData = [
  {
    id: 98,
    account_id: 'ENTITYPHJJN8ZVVUKS',
    marketplace_id: 'A2EUQ1WTGCTBG2',
    optimization_name: 'LGE Canada Opt Set 1',
    ad_budget_type: 'DATERANGE',
    ad_budget_amount: 14112,
    ad_budget_start_date: '2024-10-04',
    ad_budget_end_date: '2024-12-31',
    optimization_range: 'SPPLUS',
    optimization_goal: 'SALES',
    optimization_option: 'NONE',
    optimization_target_type: 'NONE',
    optimization_target_value: 0,
    bid_yn: 'Y',
    use_yn: 'Y',
    created_by: 4,
    creation_datetime: '2024-10-15T16:43:06',
    updated_by: 4,
    last_update_datetime: '2024-12-10T05:21:03',
    request_status: 'DONE',
    portfolio_id: '**************',
    account_type: 'seller',
    selling_partner_id: 'SELLER123',
    display_yn: 'Y'
  }
];

const budgetPacingMockData = [
  {
    id: 98,
    optimization_name: 'LGE Canada Opt Set 1',
    ad_budget_amount: 14112,
    ad_budget_start_date: '2024-10-04',
    ad_budget_end_date: '2024-12-31',
    daily_spending_history: {},
    is_ended: false,
    custom_date_range: true,
    ad_budget_type: 'DATERANGE',
    budget_usage_predictions: {
      MAX: 1200,
      MIN: 800,
      TARGET: 1000,
      estimated_budget_state: 'NORMAL'
    }
  }
];

const targetCandidateAsinsMockData = [
  {
    asin: 'B000TEST01',
    sku: 'SKU-001',
    item_name: 'Sample Product A',
    image: ''
  },
  {
    asin: 'B000TEST02',
    sku: 'SKU-002',
    item_name: 'Standard Widget',
    image: ''
  },
  {
    asin: 'B000TEST03',
    sku: 'SKU-003',
    item_name: 'Stanley Mug',
    image: ''
  }
];

// Main mock setup function for OptimizationSets
export const setupOptimizationSetsMocks = () => {
  // Mock member API
  cy.intercept('GET', '/api/member', {
    statusCode: 200,
    body: memberMockData
  }).as('getMember');

  // Mock optimization sets API
  cy.intercept('GET', '/api/optimization/list_optimizations*', {
    statusCode: 200,
    body: optimizationSetsMockData
  }).as('getListOptimizations');

  // Mock available profiles
  cy.intercept('GET', '/api/member/available_profiles*', {
    statusCode: 200,
    body: []
  }).as('getAvailableProfiles');

  // Mock budget pacing
  cy.intercept('GET', '/api/hourlyReport/new_budget_pacing*', {
    statusCode: 200,
    body: budgetPacingMockData
  }).as('getNewBudgetPacing');

  // Mock target candidate ASINs
  cy.intercept('GET', '/api/optimization/list_target_candidate_asins*', {
    statusCode: 200,
    body: targetCandidateAsinsMockData
  }).as('getListTargetCandidateAsins');
};
