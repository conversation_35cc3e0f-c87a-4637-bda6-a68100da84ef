# E2E Tests Structure - Clean and Organized

## 📋 Overview

Cleaned up test structure with dedicated mock files and no unnecessary dependencies.

## 📁 File Structure

### **Test Files**
- `OptimizationSets.module.cy.js` - Basic OptimizationSets functionality tests
- `AddTargetModal.module.cy.js` - Comprehensive AddTargetModal tests

### **Mock Files**
- `AddTargetModalMock.js` - All mocks and selectors for AddTargetModal
- `OptimizationSetsSimpleMock.js` - Simple mocks for OptimizationSets
- `OptimizationSetsMock.js` - Original OptimizationSets mock (if needed)

### **Page Objects**
- `AddTargetModalPage.js` - Page Object for AddTargetModal
- `OptimizationSetsPage.js` - Page Object for OptimizationSets

## 🧪 Test Coverage

### **OptimizationSets.module.cy.js** (6 test cases)
- Display vendor dropdown on first access
- Display list of existing optimization sets
- Display add new button
- Allow form input for new optimization sets
- Display period, budget, and usage when clicking optimization set
- Display edit and pause buttons

### **AddTargetModal.module.cy.js** (14 test cases)
- **Navigation Flow** (2 tests)
- **Form Validation** (5 tests)
- **Successful Operations** (2 tests)
- **Error Handling** (1 test)
- **Accessibility** (2 tests)
- **Performance** (2 tests)

## 🔧 Mock Structure

### **AddTargetModalMock.js**
```javascript
// Mock functions
setupAddTargetModalMocks()        // Main setup
setupAddTargetModalErrorMocks()   // Error scenarios
setupSlowAddTargetMock()          // Performance testing

// Test data
validationTestData                // Form validation data

// Selectors
addTargetModalSelectors           // All component selectors
```

### **OptimizationSetsSimpleMock.js**
```javascript
// Mock function
setupOptimizationSetsMocks()      // Simple setup for basic tests
```

## 🚀 How to Run

```bash
# Start dev server
yarn dev

# Run OptimizationSets tests
yarn cypress run --spec "tests/module/specs/OptimizationSets.module.cy.js"

# Run AddTargetModal tests
yarn cypress run --spec "tests/module/specs/AddTargetModal.module.cy.js"

# Run all module tests
yarn test:module

# GUI mode
yarn cypress:open
```

## 🎯 Key Benefits

1. **No unnecessary dependencies** - Removed OptimizationSetsSwitchMock
2. **Clean separation** - Each test file has its own dedicated mock
3. **No inline API mocks** - All cy.intercept calls moved to mock files
4. **Focused testing** - OptimizationSets for basic functionality, AddTargetModal for complex scenarios
5. **Easy maintenance** - Clear structure with dedicated mock files

## 📝 API Endpoints Mocked

### AddTargetModal:
- `GET /api/member`
- `GET /api/optimization/list_optimizations*`
- `GET /api/optimization/list_targets*`
- `POST /api/optimization/targets`
- `GET /api/member/available_profiles*`
- `GET /api/hourlyReport/new_budget_pacing*`
- `GET /api/hourlyReport/campaign-budget-usage*`
- `GET /api/optimization/list_target_candidate_asins*`

### OptimizationSets:
- `GET /api/member`
- `GET /api/optimization/list_optimizations*`
- `GET /api/member/available_profiles*`
- `GET /api/hourlyReport/new_budget_pacing*`
- `GET /api/optimization/list_target_candidate_asins*`

## 🔍 Usage Examples

### Using AddTargetModal mock:
```javascript
import { setupAddTargetModalMocks, validationTestData } from '../mock/AddTargetModalMock';

beforeEach(() => {
  cy.login();
  setupAddTargetModalMocks();
});
```

### Using OptimizationSets mock:
```javascript
import { setupOptimizationSetsMocks } from '../mock/OptimizationSetsSimpleMock';

beforeEach(() => {
  cy.login();
  setupOptimizationSetsMocks();
});
```

This structure provides clean separation of concerns with dedicated mock files for each test suite, making the tests easier to maintain and understand.
