/// <reference types="cypress" />

import AddTargetModalPage from '../../pages/AddTargetModalPage';
import {
  setupAddTargetModalMocks,
  setupAddTargetModalErrorMocks,
  validationTestData,
} from '../mock/AdPortfolio';

describe('AddTargetModal E2E Tests', () => {
  const page = new AddTargetModalPage();

  beforeEach(() => {
    cy.login();
    // Set up mock API responses
    setupAddTargetModalMocks();
    // Navigate to dashboard
    cy.visit('/dashboard');
  });

  describe('Modal Opening and Closing', () => {
    beforeEach(() => {
      page.navigateToAdGroupTargets();
    });

    it('should open AddTargetModal when Add Target button is clicked', () => {
      page.openModal().assertModalTitle('Add Target');
    });

    it('should close modal when close button is clicked', () => {
      page.openModal().clickCloseButton().assertModalIsClosed();
    });

    it('should close modal when cancel button is clicked', () => {
      page.openModal().clickCancelButton().assertModalIsClosed();
    });
  });

  describe('Keyword Target Form', () => {
    beforeEach(() => {
      // Open modal for keyword target
      cy.get(page.targets.addButton).click();
      // Assume keyword target type is selected by default or select it
    });

    it('should display keyword form fields for keyword target type', () => {
      page.assertKeywordInputExists();
      page.assertBidInputExists();
      page.assertMatchTypeDropdownExists();
    });

    it('should validate required keyword field', () => {
      page.clickAddButton();
      page.assertErrorMessage('Keyword is required');
    });

    it('should validate keyword minimum length', () => {
      page.typeKeyword(validationTestData.invalidKeyword.tooShort);
      page.clickAddButton();
      page.assertErrorMessage('Keyword must be at least 2 characters long');
    });

    it('should validate required bid field', () => {
      page.typeKeyword(validationTestData.validKeyword.keyword);
      page.clickAddButton();
      page.assertErrorMessage('Bid is required');
    });

    it('should validate bid minimum value', () => {
      page.typeKeyword(validationTestData.validKeyword.keyword);
      page.typeBid(validationTestData.invalidBid.negative);
      page.clickAddButton();
      page.assertErrorMessage('Bid must be greater than 0');
    });

    it('should successfully add keyword target with valid data', () => {
      const testData = validationTestData.validKeyword;
      page.typeKeyword(testData.keyword);
      page.typeBid(testData.bid);
      page.selectMatchType(testData.matchType);

      page.clickAddButton();

      cy.wait('@addTarget').then((interception) => {
        expect(interception.request.body).to.include({
          keyword: testData.keyword,
          bid: testData.bid,
          match_type: 'EXACT',
        });
      });

      page.assertModalIsClosed();
    });
  });

  describe('Product Target Form', () => {
    beforeEach(() => {
      cy.get(page.targets.addButton).click();
      // Select product target type
      cy.get(page.targets.targetTypeProduct).click();
    });

    it('should display ASIN form fields for product target type', () => {
      page.assertAsinInputExists();
      page.assertBidInputExists();
    });

    it('should validate required ASIN field', () => {
      page.clickAddButton();
      page.assertErrorMessage('ASIN is required');
    });

    it('should validate ASIN format', () => {
      page.typeAsin('invalid');
      page.clickAddButton();
      page.assertErrorMessage(
        'ASIN must be 10 characters long and contain only letters and numbers'
      );
    });

    it('should successfully add product target with valid ASIN', () => {
      page.typeAsin('B08N5WRWNW');
      page.typeBid('2.00');

      page.clickAddButton();

      cy.wait('@addTarget').then((interception) => {
        expect(interception.request.body).to.include({
          asin: 'B08N5WRWNW',
          bid: '2.00',
        });
      });

      page.assertModalIsClosed();
    });
  });

  describe('Switch Component Tests', () => {
    beforeEach(() => {
      // Navigate to target list view where switches are displayed
      cy.visit('/dashboard');
      // Click on a product to open target list
      cy.get('[data-testid="product-item"]').first().click();
      // Click on an ad group to show targets
      cy.get('[data-testid="ad-group-item"]').first().click();
    });

    it('should display switch components for each target', () => {
      page.assertSwitchExists();
    });

    it('should show enabled state for active targets', () => {
      // Assuming first target is enabled
      page.getSwitchInTargetItem(0).should('be.checked');
    });

    it('should show disabled state for paused targets', () => {
      // Assuming second target is paused
      page.getSwitchInTargetItem(1).should('not.be.checked');
    });

    it('should toggle target status when switch is clicked', () => {
      // Get initial state
      page.getSwitchInTargetItem(0).then(($switch) => {
        const wasChecked = $switch.is(':checked');

        // Click the switch
        page.getSwitchInTargetItem(0).click({ force: true });

        // Verify API call
        cy.wait('@changeTargetStatus').then((interception) => {
          const expectedState = wasChecked ? 'PAUSED' : 'ENABLED';
          expect(interception.request.url).to.include(`state=${expectedState}`);
        });

        // Verify switch state changed
        page
          .getSwitchInTargetItem(0)
          .should(wasChecked ? 'not.be.checked' : 'be.checked');
      });
    });

    it('should show loading state during switch toggle', () => {
      // Click switch
      page.getSwitchInTargetItem(0).click({ force: true });

      // Should be disabled during loading
      page.getSwitchInTargetItem(0).should('be.disabled');

      // Wait for API response
      cy.wait('@changeTargetStatus');

      // Should be enabled again after loading
      page.getSwitchInTargetItem(0).should('not.be.disabled');
    });

    it('should handle API error gracefully', () => {
      // Mock API error
      cy.intercept('PUT', '/api/optimization/change_status_of_target*', {
        statusCode: 500,
        body: { status: false, message: 'Server error' },
      }).as('changeTargetStatusError');

      // Click switch
      page.getSwitchInTargetItem(0).click({ force: true });

      cy.wait('@changeTargetStatusError');

      // Switch should return to original state
      // Error handling behavior depends on implementation
    });
  });

  describe('API Integration', () => {
    it('should handle API loading states', () => {
      cy.get('[data-testid="add-target-button"]').click();

      page.typeKeyword('test keyword');
      page.typeBid('1.50');

      // Mock slow API response
      cy.intercept('POST', '/api/optimization/targets', (req) => {
        req.reply((res) => {
          res.delay(2000);
          res.send({ status: true });
        });
      }).as('slowAddTarget');

      page.clickAddButton();

      // Should show loading spinner
      page.assertLoadingSpinner();

      cy.wait('@slowAddTarget');

      // Loading should disappear
      page.assertNoLoadingSpinner();
      page.assertModalIsClosed();
    });

    it('should handle API errors', () => {
      cy.intercept('POST', '/api/optimization/targets', {
        statusCode: 400,
        body: { status: false, message: 'Invalid target data' },
      }).as('addTargetError');

      cy.get('[data-testid="add-target-button"]').click();

      page.typeKeyword('test keyword');
      page.typeBid('1.50');
      page.clickAddButton();

      cy.wait('@addTargetError');

      page.assertApiError('Invalid target data');
      page.assertModalIsOpen(); // Modal should stay open on error
    });
  });
});
