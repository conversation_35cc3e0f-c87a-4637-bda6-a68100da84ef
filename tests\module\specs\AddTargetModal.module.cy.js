/// <reference types="cypress" />

import AddTargetModalPage from '../../pages/AddTargetModalPage';
import {
  setupAddTargetModalMocks,
  setupAddTargetModalErrorMocks,
  setupSlowAddTargetMock,
  validationTestData,
} from '../mock/AddTargetModalMock';

describe('AddTargetModal E2E Tests', () => {
  const page = new AddTargetModalPage();

  beforeEach(() => {
    cy.login();
    setupAddTargetModalMocks();
  });

  describe('Navigation Flow', () => {
    it('Should navigate from OptimizationSets to AddTargetModal', () => {
      // Navigate to target list
      page.navigateToAdGroupTargets();
      
      // Verify Add Target button appears
      cy.get(page.navigation.addTargetButton).should('be.visible');
      
      // Open AddTargetModal
      page.openModal();
      page.assertModalTitle('Add Target');
    });

    it('Should display both AddTarget button and Switch components in same view', () => {
      page.navigateToAdGroupTargets();
      
      // Verify both components are displayed
      page.assertSwitchExists();
      cy.get(page.navigation.addTargetButton).should('be.visible');
    });
  });

  describe('Form Validation', () => {
    beforeEach(() => {
      page.navigateToAdGroupTargets();
      page.openModal();
    });

    it('Should display form fields for keyword target', () => {
      page.assertKeywordInputExists();
      page.assertBidInputExists();
      page.assertMatchTypeDropdownExists();
    });

    it('Should validate required keyword', () => {
      page.clickAddButton();
      page.assertErrorMessage('Keyword is required');
    });

    it('Should validate minimum keyword length', () => {
      page.typeKeyword(validationTestData.invalidKeyword.tooShort);
      page.clickAddButton();
      page.assertErrorMessage('Keyword must be at least 2 characters long');
    });

    it('Should validate required bid', () => {
      page.typeKeyword(validationTestData.validKeyword.keyword);
      page.clickAddButton();
      page.assertErrorMessage('Bid is required');
    });

    it('Should validate minimum bid amount', () => {
      page.typeKeyword(validationTestData.validKeyword.keyword);
      page.typeBid(validationTestData.invalidBid.negative);
      page.clickAddButton();
      page.assertErrorMessage('Bid must be greater than 0');
    });
  });

  describe('Successful Operations', () => {
    beforeEach(() => {
      page.navigateToAdGroupTargets();
      page.openModal();
    });

    it('Should successfully add keyword target', () => {
      const testData = validationTestData.validKeyword;
      
      page.typeKeyword(testData.keyword);
      page.typeBid(testData.bid);
      page.selectMatchType(testData.matchType);
      
      page.clickAddButton();
      
      cy.wait('@addTarget').then((interception) => {
        expect(interception.request.body).to.include({
          keyword: testData.keyword,
          bid: testData.bid,
          match_type: 'EXACT',
        });
      });
      
      page.assertModalIsClosed();
    });

    it('Should refresh target list after adding target', () => {
      const testData = validationTestData.validKeyword;
      
      page.typeKeyword(testData.keyword);
      page.typeBid(testData.bid);
      page.selectMatchType(testData.matchType);
      
      page.clickAddButton();
      
      cy.wait('@addTarget');
      // Target list should be refreshed
      cy.wait('@getTargets');
      
      page.assertModalIsClosed();
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      page.navigateToAdGroupTargets();
    });

    it('Should handle add target API errors gracefully', () => {
      setupAddTargetModalErrorMocks();

      page.openModal();

      const testData = validationTestData.validKeyword;
      page.typeKeyword(testData.keyword);
      page.typeBid(testData.bid);
      page.selectMatchType(testData.matchType);

      page.clickAddButton();

      cy.wait('@addTargetError');

      // Modal should remain open on error
      page.assertModalIsOpen();
      page.assertApiError('Failed to add target');
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      page.navigateToAdGroupTargets();
    });

    it('Should support keyboard navigation for Add Target button', () => {
      cy.get(page.navigation.addTargetButton).focus();
      cy.get(page.navigation.addTargetButton).should('be.focused');
      
      // Should open modal with Enter key
      cy.get(page.navigation.addTargetButton).type('{enter}');
      page.assertModalIsOpen();
    });

    it('Should close modal with Escape key', () => {
      page.openModal();
      
      // Close modal with Escape
      cy.get('body').type('{esc}');
      page.assertModalIsClosed();
    });
  });

  describe('Performance', () => {
    beforeEach(() => {
      page.navigateToAdGroupTargets();
    });

    it('Should display loading states appropriately', () => {
      // Setup slow API mock
      setupSlowAddTargetMock();

      page.openModal();

      const testData = validationTestData.validKeyword;
      page.typeKeyword(testData.keyword);
      page.typeBid(testData.bid);
      page.selectMatchType(testData.matchType);

      page.clickAddButton();

      // Should display loading spinner
      page.assertLoadingSpinner();

      cy.wait('@slowAddTarget');

      // Loading should disappear
      page.assertNoLoadingSpinner();
      page.assertModalIsClosed();
    });

    it('Should prevent double submission', () => {
      page.openModal();
      
      const testData = validationTestData.validKeyword;
      page.typeKeyword(testData.keyword);
      page.typeBid(testData.bid);
      page.selectMatchType(testData.matchType);
      
      // Click Add button multiple times quickly
      page.clickAddButton();
      page.clickAddButton();
      page.clickAddButton();
      
      // Should only have 1 API call
      cy.wait('@addTarget');
      cy.get('@addTarget.all').should('have.length', 1);
    });
  });
});
