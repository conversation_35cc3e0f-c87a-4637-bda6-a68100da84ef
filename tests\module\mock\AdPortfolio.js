/// <reference types="cypress" />

export const memberMock = {
  "id": "mem_1",
  "email": "<EMAIL>",
  "lwa_accounts": [
    {
      "account_id": "acc_vendor_1",
      "account_type": "vendor",
      "vendor_code": "VENDOR1",
      "marketplaces": [
        {
          "id": "mk_vendor_us",
          "marketplace_id": "ATVPDKIKX0DER",
          "marketplace_name": "United States",
          "country_code": "US",
          "default_currency_code": "USD",
          "subscription_yn": "Y",
          "ad_lwa_validation_yn": "Y",
          "sp_lwa_validation_yn": "Y",
          "subscription_features": { "number_of_optimization_sets": 5 }
        }
      ]
    }
  ]
};

export const targetsMock = [
  {
    "target_id": "target_1",
    "target": "wireless headphones",
    "target_type": "KEYWORD",
    "match_type": "EXACT",
    "state": "ENABLED",
    "negative": false,
    "bid": 1.50,
    "campaign_id": "campaign_1",
    "ad_group_id": "adgroup_1"
  },
  {
    "target_id": "target_2", 
    "target": "B08N5WRWNW",
    "target_type": "PRODUCT",
    "state": "PAUSED",
    "negative": false,
    "bid": 2.00,
    "campaign_id": "campaign_1",
    "ad_group_id": "adgroup_1"
  },
  {
    "target_id": "target_3",
    "target": "bluetooth speakers",
    "target_type": "KEYWORD", 
    "match_type": "PHRASE",
    "state": "ENABLED",
    "negative": false,
    "bid": 1.25,
    "campaign_id": "campaign_1",
    "ad_group_id": "adgroup_1"
  },
  {
    "target_id": "target_4",
    "target": "cheap headphones",
    "target_type": "KEYWORD",
    "match_type": "EXACT", 
    "state": "PAUSED",
    "negative": true,
    "campaign_id": "campaign_1",
    "ad_group_id": "adgroup_1"
  },
  {
    "target_id": "target_5",
    "target": "B07XJ8C8F5",
    "target_type": "PRODUCT",
    "state": "ENABLED", 
    "negative": true,
    "campaign_id": "campaign_1",
    "ad_group_id": "adgroup_1"
  }
];

export const campaignsMock = [
  {
    "campaign_id": "campaign_1",
    "campaign_name": "Test Campaign 1",
    "campaign_state": "ENABLED",
    "campaign_targeting_settings": "MANUAL",
    "campaign_start_date": "2024-01-01",
    "campaign_mop_yn": "Y",
    "ad_groups": [
      {
        "ad_group_id": "adgroup_1",
        "ad_group_name": "Test Ad Group 1",
        "ad_group_state": "ENABLED",
        "ad_group_type": "KEYWORD",
        "ad_group_creation_date_time": "2024-01-01T00:00:00Z"
      },
      {
        "ad_group_id": "adgroup_2", 
        "ad_group_name": "Test Ad Group 2",
        "ad_group_state": "PAUSED",
        "ad_group_type": "PRODUCT",
        "ad_group_creation_date_time": "2024-01-02T00:00:00Z"
      }
    ]
  }
];

export const productMock = {
  "asin": "B08N5WRWNW",
  "product_name": "Test Wireless Headphones",
  "campaigns": campaignsMock,
  "inventory": {
    "estimated_inventory_state": "NORMAL",
    "available_quantity": 100,
    "estimated_daily_units_sold": 5
  }
};

export const portfolioMock = {
  "id": 1,
  "account_id": "acc_vendor_1",
  "marketplace_id": "ATVPDKIKX0DER",
  "ad_budget_amount": 1000,
  "ad_budget_end_date": "2024-12-31",
  "target_products": [productMock]
};

export const addTargetSuccessResponse = {
  "status": true,
  "message": "Target added successfully"
};

export const addTargetErrorResponse = {
  "status": false,
  "message": "Failed to add target",
  "detail": "Invalid target data provided"
};

export const changeTargetStatusSuccessResponse = {
  "status": true,
  "message": "Target status updated successfully"
};

export const changeTargetStatusErrorResponse = {
  "status": false,
  "message": "Failed to update target status",
  "detail": "Target not found"
};

// Mock interceptors setup function
export const setupAddTargetModalMocks = () => {
  cy.intercept('GET', '/api/member', {
    statusCode: 200,
    body: memberMock
  }).as('getMember');

  cy.intercept('GET', '/api/optimization/list_targets*', {
    statusCode: 200,
    body: targetsMock
  }).as('getTargets');

  cy.intercept('POST', '/api/optimization/targets', {
    statusCode: 200,
    body: addTargetSuccessResponse
  }).as('addTarget');

  cy.intercept('PUT', '/api/optimization/change_status_of_target*', {
    statusCode: 200,
    body: changeTargetStatusSuccessResponse
  }).as('changeTargetStatus');

  // Mock campaign budget usage
  cy.intercept('GET', '/api/hourlyReport/campaign-budget-usage*', {
    statusCode: 200,
    body: {
      asins: [
        {
          asin: "B08N5WRWNW",
          campaigns: [
            {
              campaign_id: "campaign_1",
              total_budget_usage: 150.50,
              today_budget_usage: 25.75
            }
          ]
        }
      ]
    }
  }).as('getCampaignBudgetUsage');
};

// Error scenarios setup
export const setupAddTargetModalErrorMocks = () => {
  cy.intercept('POST', '/api/optimization/targets', {
    statusCode: 400,
    body: addTargetErrorResponse
  }).as('addTargetError');

  cy.intercept('PUT', '/api/optimization/change_status_of_target*', {
    statusCode: 500,
    body: changeTargetStatusErrorResponse
  }).as('changeTargetStatusError');
};

// Validation test data
export const validationTestData = {
  validKeyword: {
    keyword: "wireless headphones",
    bid: "1.50",
    matchType: "Exact"
  },
  validAsin: {
    asin: "B08N5WRWNW", 
    bid: "2.00"
  },
  invalidKeyword: {
    tooShort: "a",
    empty: ""
  },
  invalidAsin: {
    tooShort: "B08N5",
    invalidFormat: "invalid-asin",
    empty: ""
  },
  invalidBid: {
    negative: "-1",
    zero: "0",
    empty: "",
    nonNumeric: "abc"
  }
};

// Form field selectors
export const selectors = {
  modal: {
    container: '[role="dialog"]',
    title: '[role="dialog"] h3',
    closeButton: '[role="dialog"] button[aria-label="Close"]',
    cancelButton: 'button:contains("Cancel")',
    addButton: 'button:contains("Add")'
  },
  form: {
    keywordInput: 'input[placeholder="Enter keyword"]',
    asinInput: 'input[placeholder*="Enter ASIN"]',
    bidInput: 'input[placeholder="Enter bid amount"]',
    matchTypeButton: 'button[role="combobox"]',
    matchTypeDropdown: '[role="listbox"]'
  },
  errors: {
    fieldError: '.text-red-600',
    apiError: '.text-red-600'
  },
  loading: {
    spinner: '.MuiCircularProgress-root'
  },
  switch: {
    container: '.MuiSwitch-root',
    input: '.MuiSwitch-root input[type="checkbox"]',
    targetListItem: 'li.p-4'
  },
  targets: {
    addButton: '[data-testid="add-target-button"]',
    productItem: '[data-testid="product-item"]',
    adGroupItem: '[data-testid="ad-group-item"]',
    targetTypeProduct: '[data-testid="target-type-product"]',
    targetTypeKeyword: '[data-testid="target-type-keyword"]'
  }
};
