"use client"

import { cn } from "@/utils/msc"
import { getCookie, setCookie } from "cookies-next"
import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import { ChevronRightIcon, ChevronUpDownIcon, GlobeAmericasIcon, UsersIcon } from "@heroicons/react/20/solid"
import ProfileSelect, { ProfileOption } from "@/components/dashboard/profile-select"
import MarketplaceSelect, { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import { useTranslations } from "next-intl"
import LocaleSwitcher from "../ui/locale-switcher"
import Link from "next/link"

interface LNBProps {
  mopUserData: any;
  selectedProfile: ProfileOption | null;
  setSelectedProfile: (option: ProfileOption | null) => void
  selectedMarketplace: MarketplaceOption | null;
  setSelectedMarketplace: (option: MarketplaceOption | null) => void
}

export default function TNBComponent({
  mopUserData,
  selectedProfile,
  setSelectedProfile,
  selectedMarketplace,
  setSelectedMarketplace,
}: LNBProps) {
  const [isLNBExpanded, setIsLNBExpanded] = useState(true)
  const searchParams = useSearchParams()
  const tab = searchParams.get('tab')
  const [profileOptions, setProfileOptions] = useState<ProfileOption[]>([])
  const [marketplaceOptions, setMarketplaceOptions] = useState<MarketplaceOption[]>([])

  const oneDay = 24 * 60 * 60 * 1000
  const expirationDate = new Date(Date.now() + oneDay * 14)
  const t = useTranslations('component')

  useEffect(() => {
    if (mopUserData && mopUserData.lwa_accounts && mopUserData.lwa_accounts.length > 0) {
      // set default selected profile based on cookie
      const profileId = getCookie('mop-profile-id')
      const selectedProfile = profileId
        ? mopUserData.lwa_accounts.find((profile: ProfileOption) => profile.id === profileId)
        : null
      setProfileOptions(mopUserData.lwa_accounts)
      setSelectedProfile(selectedProfile ? selectedProfile : mopUserData.lwa_accounts[0])
    }
  }, [mopUserData])

  useEffect(() => {
    if (selectedProfile?.marketplaces) {
      // set default selected marketplace based on cookie
      const marketplaceId = getCookie('mop-marketplace-id')
      const marketplaceOptions = selectedProfile.marketplaces.filter((marketplace: MarketplaceOption) => {
        return !marketplace.marketplace_name.includes('Non-Amazon') && (marketplace.country_code === 'US' ? marketplace.marketplace_id === "ATVPDKIKX0DER" : true)
      })
      const selectedMarketplace = marketplaceId
        ? marketplaceOptions.find((marketplace: MarketplaceOption) => marketplace.id === marketplaceId)
        : null
      const USMarketplace = marketplaceOptions.find((marketplace: MarketplaceOption) => marketplace.country_code === 'US')
      setMarketplaceOptions(marketplaceOptions)
      setSelectedMarketplace(selectedMarketplace
        ? selectedMarketplace
        : USMarketplace
          ? USMarketplace
          : marketplaceOptions[0]
      )
      setCookie('mop-profile-id', selectedProfile.id + "", {expires: expirationDate})
    }
  }, [selectedProfile])

  useEffect(() => {
    if (selectedMarketplace) {
      setCookie('mop-marketplace-id', selectedMarketplace.id + "", {expires: expirationDate})
    }
  }, [selectedMarketplace])
  
  return (
		<header className="absolute inset-x-0 w-full flex justify-center border-b border-gray-200 bg-white z-10">
			<div className="flex items-center justify-between w-full h-16 px-4 mx-auto sm:px-6">
				<div className="flex items-center gap-x-1">
					{ tab === "manage-profile" || tab === "manage-billing"
						? <div
							className={cn(
								"relative transition-width ease-in-out delay-150",
								"w-[180px]"
							)}>
							<div
								className={cn(
									"group relative w-full h-9 py-2 pl-3 pr-6 cursor-not-allowed rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300 bg-gray-100",
								)}
							>
								<span className="block truncate text-gray-500 group-hover:text-gray-600">
									{t('status.notAvailable')}
								</span>
								<span
									className={cn(
										"pointer-events-none absolute inset-y-0 flex items-center px-2",
										"right-0"
									)}
								>
									<ChevronUpDownIcon
										className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
										aria-hidden="true"
									/>
								</span>
							</div>
						</div>
						: selectedProfile
							? <ProfileSelect
									className={cn(
										"transition-width ease-in-out delay-150",
										isLNBExpanded ? "w-[180px]" : "w-[36px]"
									)}
									listboxClassName={"absolute z-[10] mt-1 max-h-[360px] w-[400px] overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm divide-y divide-gray-100"}
									isLNBExpanded={isLNBExpanded}
									profileOptions={profileOptions}
									selected={selectedProfile}
									setSelected={setSelectedProfile}
								/>
							: <div
								className={cn(
									"relative transition-width ease-in-out delay-150",
									isLNBExpanded ? "w-[180px]" : "w-[36px]"
								)}>
									<div
										className={cn(
											"group relative w-full h-9 py-2 pl-3 pr-6 cursor-not-allowed rounded-lg text-left focus:outline-none text-sm border border-gray-200 hover:border-gray-300",
										)}
									>
										<span className="block truncate text-gray-500 group-hover:text-gray-600">
											
											{ isLNBExpanded
												? t('accounts.notConnected')
												: ""
											}
										</span>
										<span
											className={cn(
												"pointer-events-none absolute inset-y-0 flex items-center px-2",
												isLNBExpanded ? "right-0" : "left-0"
											)}
										>
											{ isLNBExpanded
												? (
													<ChevronUpDownIcon
														className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
														aria-hidden="true"
													/>
												)
												: (
													<UsersIcon
														className="h-5 w-5 text-gray-500 group-hover:text-gray-600"
														aria-hidden="true"
													/>
												)
											}
										</span>
									</div>
							</div>
					}
					<ChevronRightIcon className="h-6 w-6 text-gray-300" />
					{ tab === "manage-profile" || tab === "manage-billing"
						? <div
							className={cn(
								"relative transition-width ease-in-out delay-150",
								"w-[180px]"
							)}>
							<div
								className={cn(
									"group relative flex items-center gap-x-2 px-2 w-full h-9 py-1.5 cursor-not-allowed rounded-lg text-left focus:outline-none text-sm overflow-hidden border border-gray-200 hover:border-gray-300 bg-gray-100",
								)}
							>
								<GlobeAmericasIcon
									className="flex-shrink-0 h-5 w-5 text-gray-500 group-hover:text-gray-600"
									aria-hidden="true"
								/>
								<span className="flex-shrink-0 block truncate text-gray-500 group-hover:text-gray-600">
									{t('status.notAvailable')}
								</span>
								<div className="pointer-events-none flex items-center ml-auto">
									<ChevronUpDownIcon
										className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
										aria-hidden="true"
									/>
								</div>
							</div>
						</div>
						: selectedMarketplace
							? <MarketplaceSelect
									className={cn(
										"transition-width ease-in-out delay-150",
										isLNBExpanded ? "min-w-[270px]" : "w-[36px]"
									)}
									listboxClassName={"absolute z-[10] mt-1 max-h-[360px] w-[340px] overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm divide-y divide-gray-100"}
									isLNBExpanded={isLNBExpanded}
									marketplaceOptions={marketplaceOptions}
									selected={selectedMarketplace}
									setSelected={setSelectedMarketplace}
								/>
							: <div
								className={cn(
									"relative transition-width ease-in-out delay-150",
									isLNBExpanded ? "w-[270px]" : "w-[36px]"
								)}>
									<div
										className={cn(
											"group relative flex items-center gap-x-2 px-2 w-full h-9 py-1.5 cursor-not-allowed rounded-lg text-left focus:outline-none text-sm overflow-hidden border border-gray-200 hover:border-gray-300",
										)}
									>
										<GlobeAmericasIcon
											className="flex-shrink-0 h-5 w-5 text-gray-500 group-hover:text-gray-600"
											aria-hidden="true"
										/>
										<span className="flex-shrink-0 block truncate text-gray-500 group-hover:text-gray-600">
											{ isLNBExpanded
												? t('accounts.notConnected')
												: ""
											}
										</span>
										<div className="pointer-events-none flex items-center ml-auto">
											{ isLNBExpanded
												? (
													<ChevronUpDownIcon
														className="h-5 w-5 text-gray-300 group-hover:text-gray-400"
														aria-hidden="true"
													/>
												)
												: (
													<></>
												)
											}
										</div>
								</div>
							</div>
					}
					{ tab === "manage-profile" || tab === "manage-billing"
						? <></>
						: selectedMarketplace?.subscription_yn === "Y"
							? ""
							: <Link
									href={{
										pathname: '/dashboard',
										search: '?tab=manage-profile'
									}}
									className={cn(
										"group inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
										"bg-gray-700 text-gray-100 hover:bg-gray-600 hover:text-white",
										"relative h-8 py-4 px-4 ml-2 justify-start text-xs",
									)}
								>
									<span className="relative flex-shrink-0 block">
										<span className="block absolute -right-1.5 -top-0.5 w-1.5 h-1.5 rounded-full bg-red-400 group-hover:bg-red-500"></span>
										subscribe
									</span>
								</Link>
					}
				</div>
				<div className="flex items-center gap-x-4">
					<LocaleSwitcher />
				</div>
			</div>
		</header>
  )
}
