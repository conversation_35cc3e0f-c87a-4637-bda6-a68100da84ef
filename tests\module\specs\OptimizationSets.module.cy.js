// import OptimizationSetsMock from "../mock/OptimizationSetsMock";
import OptimizationSetsPage from "../../pages/OptimizationSetsPage";

const page = new OptimizationSetsPage();

describe('OptimizationSets', () => {
    beforeEach(() => {
        cy.login();
        cy.intercept('GET', '/api/member', {
            body: {
                "id": "mem_1",
                "email": "<EMAIL>",
                "lwa_accounts": [
                    {
                        "account_id": "acc_1",
                        "account_type": "seller",
                        "seller_id": "SELLER123",
                        "marketplaces": [
                            {
                                "id": "mk_seller_us",
                                "marketplace_id": "ATVPDKIKX0DER",
                                "marketplace_name": "United States",
                                "country_code": "US",
                                "default_currency_code": "USD",
                                "subscription_yn": "Y",
                                "ad_lwa_validation_yn": "Y",
                                "sp_lwa_validation_yn": "Y",
                                "subscription_features": { "number_of_optimization_sets": 5 }
                            }
                        ],
                    },
                    {
                        "account_id": "acc_2",
                        "account_type": "vendor",
                        "vendor_code": "VENDOR1",
                        "marketplaces": [
                            {
                                "id": "mk_vendor_us",
                                "marketplace_id": "ATVPDKIKX0DER",
                                "marketplace_name": "United States",
                                "country_code": "US",
                                "default_currency_code": "USD",
                                "subscription_yn": "Y",
                                "ad_lwa_validation_yn": "Y",
                                "sp_lwa_validation_yn": "Y",
                                "subscription_features": { "number_of_optimization_sets": 5 }
                            }
                        ],
                    }
                ]
            }
        }).as('getMember');
        cy.intercept('GET', '/api/oauth/availableProfiles', { 
            body: [{
                "id": "acc_1PeUKDGMi5qS2Zp1",
                "name": "Test Profile",
                "email": "<EMAIL>",
                "created_at": "2024-01-01T00:00:00.000Z",
                "updated_at": "2024-01-01T00:00:00.000Z",
                "profiles": []
            }] 
        }).as('getAvailableProfiles');
        cy.intercept('GET', '/api/optimization/optimizations?account_id=*&marketplace_id=*', {
            body: [
                {
                    optimization_id: 1,
                    account_id: 'acc_1',
                    marketplace_id: 'ATVPDKIKX0DER',
                    portfolio_id: 'pf_1',
                    creation_datetime: '2025-01-01T00:00:00Z',
                    last_update_datetime: '2025-01-02T00:00:00Z',
                    ad_budget_amount: 1000,
                    ad_budget_end_date: '2025-12-31',
                    ad_budget_start_date: '2025-01-01',
                    ad_budget_type: 'DATERANGE',
                    use_yn: 'Y',
                    bid_yn: 'Y',
                    display_yn: 'Y',
                    optimization_goal: 'SALES',
                    optimization_name: 'Opt Set 1',
                    optimization_option: 'DEFAULT',
                    optimization_range: 'DEFAULT',
                    optimization_target_type: 'ASIN',
                    optimization_target_value: 0,
                    request_status: 'READY',
                    // optional: top product for header
                    top_product: {
                        asin: 'B000TEST01',
                        sku: 'SKU-001',
                        item_name: 'Sample Product',
                        image: '',
                        listing_price: 10,
                        eligibility_status: 'ELIGIBLE'
                    }
                }
            ]
        }).as('getListOptimizations');
        cy.intercept('POST', '/api/hourlyReport/new_budget_pacing', {
            body: [
                {
                    optimization_id: 1,
                    total_budget: 1000,
                    start_date: '2025-01-01',
                    end_date: '2025-12-31',
                    spent_budget: 100,
                    remaining_budget: 900,
                    remaining_days: 200,
                    daily_recommended_budget: 5,
                    daily_spending_history: {},
                    is_ended: false,
                    custom_date_range: true,
                    ad_budget_type: 'DATERANGE',
                    budget_usage_predictions: {
                        MAX: 1200,
                        MIN: 800,
                        TARGET: 1000,
                        estimated_budget_state: 'NORMAL'
                    }
                }
            ]
        }).as('getNewBudgetPacing');
        // Mock target candidate ASINs to prevent UI errors during search typing
        cy.intercept('GET', '/api/optimization/list_target_candidate_asins?account_id=*&marketplace_id=*', {
            body: [
                { asin: 'B000TEST01', sku: 'SKU-001', item_name: 'Sample Product A', image: '' },
                { asin: 'B000TEST02', sku: 'SKU-002', item_name: 'Standard Widget', image: '' },
                { asin: 'B000TEST03', sku: 'SKU-003', item_name: 'Stanley Mug', image: '' },
            ]
        }).as('getListTargetCandidateAsins');
    });

    it.guide('OptimizationSets 화면에 처음 접속하면,vendor를 설정할 수 있는 dropdown이 있다.', {
        actionFunc: () => {
            cy.visit('/dashboard?tab=ad-portfolio');
        },
        waitFunc: () => {
             cy.wait(['@getMember', '@getAvailableProfiles', '@getListOptimizations', '@getNewBudgetPacing']);
        },
        assertFunc: () => {
             page.assertDropDown();     
        }
    });

    it.guide('기존에 설정해둔 optimization set의 목록을 확인할 수 있다.',{
        actionFunc: () => {
            cy.visit('/dashboard?tab=ad-portfolio');
        },
        waitFunc: () => {
             cy.wait(['@getMember', '@getAvailableProfiles', '@getListOptimizations', '@getNewBudgetPacing']);
        },
        assertFunc:()=>{
            page.assertFirstItem();
        },
    })

    it.guide('새로운 opitimzation set을 생성할 수 있는 add new 버튼을 확인할 수 있다.',{
        actionFunc: () => {
            cy.visit('/dashboard?tab=ad-portfolio');
        },
        waitFunc: () => {
            cy.wait(['@getMember', '@getAvailableProfiles', '@getListOptimizations', '@getNewBudgetPacing']);
        },
        assertFunc:()=>{
            page.assertAddButton();
        },
    })

    it.guide('add new 버튼을 눌러 name, targetcandidates, objective, advertising type, schedule& budget을 입력할 수 있다.',{
        actionFunc: () => {
            cy.visit('/dashboard?tab=ad-portfolio');
            page.clickAddButton();
        },
        waitFunc: () => {
            cy.wait(['@getMember', '@getAvailableProfiles', '@getListOptimizations', '@getNewBudgetPacing', '@getListTargetCandidateAsins']);
        },
        assertFunc:()=>{
            page.typeNameInput('test');
            page.assertNameInput();

            page.typeSearchInput('stan');
            page.assertSearchInput();

            page.clickToggleButton();
            page.assertToggleButton();

            page.assertSaveButton();
        },
    })

    it.guide('optimization set을 클릭하면 설정해둔 시기, 예산, 사용량을 확인할 수 있다.',{
        actionFunc: () => {
            cy.visit('/dashboard?tab=ad-portfolio');
            page.clickFirstItem();
        },
        waitFunc: () => {
             cy.wait(['@getMember', '@getAvailableProfiles', '@getListOptimizations', '@getNewBudgetPacing']);
        },
        assertFunc:()=>{
            page.assertPeriod();
            page.assertUsage();
        },
    })  

    it.guide('수정할 수 있는 edit, 멈출 수 있는 pause 버튼을 확인할 수 있다.',{
        actionFunc: () => {
            cy.visit('/dashboard?tab=ad-portfolio');
            page.clickFirstItem();
        },
        waitFunc: () => {
             cy.wait(['@getMember', '@getAvailableProfiles', '@getListOptimizations', '@getNewBudgetPacing']);
        },
        assertFunc:()=>{
            page.assertEditButton();
            page.assertPauseButton();
        }
    })
});
  