import OptimizationSetsPage from "../../pages/OptimizationSetsPage";
import { setupOptimizationSetsMocks } from "../mock/OptimizationSetsSimpleMock";

const page = new OptimizationSetsPage();

describe('OptimizationSets', () => {
    beforeEach(() => {
        cy.login();
        setupOptimizationSetsMocks();
    });

    it.guide('Should display vendor dropdown when first accessing OptimizationSets screen', {
        actionFunc: () => {
            cy.visit('/dashboard?tab=ad-portfolio');
        },
        waitFunc: () => {
             cy.wait(['@getMember', '@getAvailableProfiles', '@getListOptimizations', '@getNewBudgetPacing']);
        },
        assertFunc: () => {
             page.assertDropDown();
        }
    });

    it.guide('Should display list of existing optimization sets',{
        actionFunc: () => {
            cy.visit('/dashboard?tab=ad-portfolio');
        },
        waitFunc: () => {
             cy.wait(['@getMember', '@getAvailableProfiles', '@getListOptimizations', '@getNewBudgetPacing']);
        },
        assertFunc:()=>{
            page.assertFirstItem();
        },
    });

    it.guide('Should display add new button to create new optimization set', {
        actionFunc: () => {
            cy.visit('/dashboard?tab=ad-portfolio');
        },
        waitFunc: () => {
             cy.wait(['@getMember', '@getAvailableProfiles', '@getListOptimizations', '@getNewBudgetPacing']);
        },
        assertFunc: () => {
             page.assertAddButton();
        }
    });

    it.guide('Should allow entering name, target candidates, objective, advertising type, schedule & budget when clicking add new button', {
        actionFunc: () => {
            cy.visit('/dashboard?tab=ad-portfolio');
            page.clickAddButton();
        },
        waitFunc: () => {
             cy.wait(['@getMember', '@getAvailableProfiles', '@getListOptimizations', '@getNewBudgetPacing', '@getListTargetCandidateAsins']);
        },
        assertFunc: () => {
            page.typeNameInput('test');
            page.assertNameInput();

            page.typeSearchInput('stan');
            page.assertSearchInput();

            page.clickToggleButton();
            page.assertToggleButton();

            page.assertSaveButton();
        }
    });

    it.guide('Should display configured period, budget, and usage when clicking optimization set',{
        actionFunc: () => {
            cy.visit('/dashboard?tab=ad-portfolio');
            page.clickFirstItem();
        },
        waitFunc: () => {
             cy.wait(['@getMember', '@getAvailableProfiles', '@getListOptimizations', '@getNewBudgetPacing']);
        },
        assertFunc:()=>{
            page.assertPeriod();
            page.assertUsage();
        },
    });

    it.guide('Should display edit and pause buttons for modification and control',{
        actionFunc: () => {
            cy.visit('/dashboard?tab=ad-portfolio');
            page.clickFirstItem();
        },
        waitFunc: () => {
             cy.wait(['@getMember', '@getAvailableProfiles', '@getListOptimizations', '@getNewBudgetPacing']);
        },
        assertFunc:()=>{
            page.assertEditButton();
            page.assertPauseButton();
        }
    });
});