import { ErrorResponse } from '@/app/api/_models';
import { getServerApiHostUrl } from '@/utils/host';
import { NextRequest, NextResponse } from 'next/server';

export type TargetsResponse = {
  id?: string;
  target: string;
  target_id: string;
  target_type: string;
  bid: any;
  state: string;
  negative: boolean;
  creation_date_time: string;
  match_type: string;
};

export async function GET(
  request: NextRequest
): Promise<NextResponse<TargetsResponse[] | ErrorResponse>> {
  const accessToken = request.headers.get('Authorization');
  if (!accessToken) {
    return NextResponse.json(
      { message: 'Authorization header is missing' },
      { status: 400 }
    );
  }
  const accountId = request.nextUrl.searchParams.get('account_id');
  if (!accountId) {
    return NextResponse.json(
      { message: 'account_id query is missing' },
      { status: 400 }
    );
  }
  const marketplaceId = request.nextUrl.searchParams.get('marketplace_id');
  if (!marketplaceId) {
    return NextResponse.json(
      { message: 'marketplace_id query is missing' },
      { status: 400 }
    );
  }
  const campaignId = request.nextUrl.searchParams.get('campaign_id');
  if (!campaignId) {
    return NextResponse.json(
      { message: 'campaign_id query is missing' },
      { status: 400 }
    );
  }
  const adGroupId = request.nextUrl.searchParams.get('ad_group_id');
  if (!adGroupId) {
    return NextResponse.json(
      { message: 'ad_group_id query is missing' },
      { status: 400 }
    );
  }
  const listTargetsResponse = await fetch(
    `${await getServerApiHostUrl()}/api/optimization/list_targets?account_id=${accountId}&marketplace_id=${marketplaceId}&campaign_id=${campaignId}&ad_group_id=${adGroupId}`,
    {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      cache: 'no-cache',
    }
  ).then((res) => res.json());

  return NextResponse.json(listTargetsResponse, { status: 200 });
}
