import { ErrorResponse } from '@/app/api/_models';
import { getServerApiHostUrl } from '@/utils/host';
import { NextRequest, NextResponse } from 'next/server';

export type AddAdGroupTargetResponse = {
  status: boolean;
  message?: string;
  detail?: string;
};

export async function POST(
  request: NextRequest
): Promise<NextResponse<AddAdGroupTargetResponse | ErrorResponse>> {
  const accessToken = request.headers.get('Authorization');
  const body = await request.json();

  if (!accessToken) {
    return NextResponse.json(
      { message: 'Authorization header is missing' },
      { status: 400 }
    );
  }

  try {
    const addAdGroupTargetResponse = await fetch(
      `${await getServerApiHostUrl()}/api/optimization/targets`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          account_id: body.account_id,
          marketplace_id: body.marketplace_id,
          campaign_id: body.campaign_id,
          ad_group_id: body.ad_group_id,
          target_type: body?.target_type,
          keyword: body?.keyword,
          match_type: body?.match_type,
          asin: body?.asin,
          bid: body?.bid,
          negative: body?.negative,
        }),
        cache: 'no-cache',
      }
    ).then((res) => res.json());

    return NextResponse.json(addAdGroupTargetResponse, { status: 200 });
  } catch (error) {
    console.error('Error adding ad group target:', error);
    return NextResponse.json(
      { message: 'Failed to add target' },
      { status: 500 }
    );
  }
}
