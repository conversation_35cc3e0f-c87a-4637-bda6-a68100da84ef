# AddTargetModal và Switch Component E2E Tests

## Tổng quan

Bộ test này bao gồm các test E2E cho AddTargetModal component và Switch component trong ứng dụng Global MOP. Tests được viết bằng Cypress và sử dụng pattern Page Object Model để dễ bảo trì.

## Cấu trúc Files

```
tests/
├── module/
│   ├── specs/
│   │   ├── AddTargetModal.module.cy.js          # Test chính cho AddTargetModal
│   │   ├── AddTargetModalSimple.module.cy.js    # Test đơn giản hóa (khuyến nghị)
│   │   └── TargetSwitch.module.cy.js            # Test riêng cho Switch component
│   └── mock/
│       └── AddTargetModalMock.js                # Mock data và setup functions
└── pages/
    └── AddTargetModalPage.js                    # Page Object Model
```

## Các Test Cases

### AddTargetModal Tests

#### 1. Modal Opening and Closing
- ✅ Mở modal khi click nút Add Target
- ✅ Đóng modal khi click nút Close
- ✅ Đóng modal khi click nút Cancel

#### 2. Keyword Target Form
- ✅ Hiển thị các trường form cho keyword target
- ✅ Validate trường keyword bắt buộc
- ✅ Validate độ dài tối thiểu của keyword (>= 2 ký tự)
- ✅ Validate trường bid bắt buộc
- ✅ Validate giá trị bid tối thiểu (> 0)
- ✅ Thêm keyword target thành công với dữ liệu hợp lệ

#### 3. Product Target Form
- ✅ Hiển thị các trường form cho product target
- ✅ Validate trường ASIN bắt buộc
- ✅ Validate định dạng ASIN (10 ký tự, chỉ chữ và số)
- ✅ Thêm product target thành công với ASIN hợp lệ

#### 4. API Integration
- ✅ Xử lý loading states
- ✅ Xử lý API errors
- ✅ Verify API calls với dữ liệu đúng

### Switch Component Tests

#### 1. Display and State
- ✅ Hiển thị switch cho mỗi target
- ✅ Hiển thị trạng thái enabled/disabled đúng
- ✅ Hiển thị cho các loại target khác nhau

#### 2. Functionality
- ✅ Toggle trạng thái target khi click switch
- ✅ Xử lý multiple switch toggles
- ✅ Hiển thị loading state trong lúc toggle

#### 3. Error Handling
- ✅ Xử lý API errors gracefully
- ✅ Xử lý network timeout
- ✅ Trở về trạng thái ban đầu khi có lỗi

#### 4. Accessibility
- ✅ Truy cập bằng bàn phím
- ✅ Có các thuộc tính ARIA phù hợp

## Cách chạy Tests

### Yêu cầu
- Node.js và yarn đã được cài đặt
- Dev server đang chạy trên port 3000

### Chạy tất cả tests
```bash
# Khởi động dev server
yarn dev

# Trong terminal khác, chạy tests
yarn test:module
```

### Chạy test cụ thể
```bash
# Chạy chỉ AddTargetModal tests
yarn cypress:run --spec "tests/module/specs/AddTargetModalSimple.module.cy.js"

# Chạy chỉ Switch tests
yarn cypress:run --spec "tests/module/specs/TargetSwitch.module.cy.js"
```

### Chạy trong GUI mode
```bash
yarn cypress:open
```

## Mock Data

Tests sử dụng mock data được định nghĩa trong `AddTargetModalMock.js`:

### Member Mock
```javascript
{
  "id": "mem_1",
  "email": "<EMAIL>",
  "lwa_accounts": [...]
}
```

### Targets Mock
```javascript
[
  {
    "target_id": "target_1",
    "target": "wireless headphones",
    "target_type": "KEYWORD",
    "state": "ENABLED",
    ...
  }
]
```

### API Endpoints được Mock
- `GET /api/member`
- `GET /api/optimization/list_targets`
- `POST /api/optimization/targets`
- `PUT /api/optimization/change_status_of_target`

## Page Object Model

### AddTargetModalPage Methods

#### Navigation
- `navigateToProductDetail()` - Điều hướng đến trang chi tiết sản phẩm
- `navigateToAdGroupTargets()` - Điều hướng đến danh sách targets
- `openModal()` - Mở AddTargetModal

#### Form Interactions
- `typeKeyword(keyword)` - Nhập keyword
- `typeAsin(asin)` - Nhập ASIN
- `typeBid(bid)` - Nhập bid
- `selectMatchType(type)` - Chọn match type

#### Assertions
- `assertModalIsOpen()` - Kiểm tra modal đã mở
- `assertErrorMessage(message)` - Kiểm tra error message
- `assertSwitchIsEnabled(index)` - Kiểm tra switch enabled

#### Utilities
- `fillFormWithValidData(type, data)` - Điền form với dữ liệu hợp lệ
- `submitAndExpectSuccess()` - Submit và expect thành công
- `verifyAddTargetApiCall(data)` - Verify API call

## Validation Rules

### Keyword Target
- Keyword: Bắt buộc, tối thiểu 2 ký tự
- Bid: Bắt buộc, > 0
- Match Type: Exact, Phrase, Broad

### Product Target
- ASIN: Bắt buộc, đúng 10 ký tự, chỉ chữ và số
- Bid: Bắt buộc, > 0

## Troubleshooting

### Common Issues

1. **Tests fail với "element not found"**
   - Kiểm tra selectors trong mock file
   - Đảm bảo dev server đang chạy
   - Kiểm tra mock data setup

2. **API calls không được intercept**
   - Kiểm tra `setupAddTargetModalMocks()` được gọi trong beforeEach
   - Verify URL patterns trong intercepts

3. **Switch tests fail**
   - Kiểm tra MUI Switch selectors
   - Đảm bảo target list được load đúng

### Debug Tips

1. Sử dụng `cy.pause()` để debug
2. Kiểm tra Network tab trong Cypress
3. Sử dụng `cy.screenshot()` để capture states
4. Log mock data: `console.log(mockData)`

## Best Practices

1. **Sử dụng Page Object Model** - Tách logic UI khỏi test cases
2. **Mock API calls** - Không phụ thuộc vào backend
3. **Test isolation** - Mỗi test độc lập
4. **Descriptive test names** - Tên test mô tả rõ ràng
5. **Setup/Teardown** - Sử dụng beforeEach/afterEach
6. **Assertions chaining** - Sử dụng method chaining cho readability

## Contributing

Khi thêm test mới:

1. Thêm mock data vào `AddTargetModalMock.js`
2. Thêm methods vào `AddTargetModalPage.js`
3. Viết test cases với tên tiếng Việt rõ ràng
4. Đảm bảo tests pass trước khi commit
5. Update README nếu cần

## Contact

Nếu có vấn đề với tests, liên hệ team QA hoặc tạo issue trong repository.
