/// <reference types="cypress" />

import BasePage from './BasePage';
import { addTargetModalSelectors } from '../module/mock/AddTargetModalMock';

class AddTargetModalPage extends BasePage {
  constructor() {
    super();
    // Import selectors from AddTargetModalMock
    this.modal = addTargetModalSelectors.modal;
    this.form = addTargetModalSelectors.form;
    this.errors = addTargetModalSelectors.errors;
    this.loading = addTargetModalSelectors.loading;
    this.switch = addTargetModalSelectors.switch;
    this.navigation = {
      optimizationSetItem: addTargetModalSelectors.optimizationSetItem,
      productItem: addTargetModalSelectors.productItem,
      adGroupItem: addTargetModalSelectors.adGroupItem,
      addTargetButton: addTargetModalSelectors.addTargetButton,
    };
  }

  // Modal interaction methods
  assertModalIsOpen() {
    cy.get(this.modal.container).should('be.visible');
    return this;
  }

  assertModalIsClosed() {
    cy.get(this.modal.container).should('not.exist');
    return this;
  }

  assertModalTitle(expectedTitle) {
    cy.get(this.modal.title).should('contain.text', expectedTitle);
    return this;
  }

  clickCloseButton() {
    cy.get(this.modal.closeButton).click();
    return this;
  }

  clickCancelButton() {
    cy.get(this.modal.cancelButton).click();
    return this;
  }

  clickAddButton() {
    cy.get(this.modal.addButton).click();
    return this;
  }

  // Form field methods
  typeKeyword(keyword) {
    cy.get(this.form.keywordInput).clear().type(keyword);
    return this;
  }

  typeAsin(asin) {
    cy.get(this.form.asinInput).clear().type(asin);
    return this;
  }

  typeBid(bid) {
    cy.get(this.form.bidInput).clear().type(bid);
    return this;
  }

  selectMatchType(matchType) {
    cy.get(this.form.matchTypeButton).click();
    cy.get(`[role="option"]:contains("${matchType}")`).click();
    return this;
  }

  clearKeyword() {
    cy.get(this.form.keywordInput).clear();
    return this;
  }

  clearAsin() {
    cy.get(this.form.asinInput).clear();
    return this;
  }

  clearBid() {
    cy.get(this.form.bidInput).clear();
    return this;
  }

  // Form field assertions
  assertKeywordInputExists() {
    cy.get(this.form.keywordInput).should('be.visible');
    return this;
  }

  assertAsinInputExists() {
    cy.get(this.form.asinInput).should('be.visible');
    return this;
  }

  assertBidInputExists() {
    cy.get(this.form.bidInput).should('be.visible');
    return this;
  }

  assertMatchTypeDropdownExists() {
    cy.get(this.form.matchTypeButton).should('be.visible');
    return this;
  }

  assertKeywordInputValue(expectedValue) {
    cy.get(this.form.keywordInput).should('have.value', expectedValue);
    return this;
  }

  assertAsinInputValue(expectedValue) {
    cy.get(this.form.asinInput).should('have.value', expectedValue);
    return this;
  }

  assertBidInputValue(expectedValue) {
    cy.get(this.form.bidInput).should('have.value', expectedValue);
    return this;
  }

  // Validation and error methods
  assertErrorMessage(message) {
    cy.get(this.errors.fieldError).should('contain.text', message);
    return this;
  }

  assertApiError(message) {
    cy.get(this.errors.apiError).should('contain.text', message);
    return this;
  }

  assertNoErrors() {
    cy.get(this.errors.fieldError).should('not.exist');
    cy.get(this.errors.apiError).should('not.exist');
    return this;
  }

  // Loading state methods
  assertLoadingSpinner() {
    cy.get(this.loading.spinner).should('be.visible');
    return this;
  }

  assertNoLoadingSpinner() {
    cy.get(this.loading.spinner).should('not.exist');
    return this;
  }

  // Target type selection methods
  selectKeywordTargetType() {
    cy.get(this.targets.targetTypeKeyword).click();
    return this;
  }

  selectProductTargetType() {
    cy.get(this.targets.targetTypeProduct).click();
    return this;
  }

  // Navigation and setup methods
  openModal() {
    cy.get(this.navigation.addTargetButton).click();
    this.assertModalIsOpen();
    return this;
  }

  navigateToOptimizationSets() {
    // Navigate to dashboard optimization sets tab
    cy.visit('/dashboard?tab=ad-portfolio');
    return this;
  }

  navigateToProductDetail() {
    // Navigate to optimization sets first
    this.navigateToOptimizationSets();
    // Wait for optimization sets to load
    cy.wait('@getListOptimizations');
    // Click on first optimization set to open portfolio view
    cy.get(this.navigation.optimizationSetItem).first().find('.py-8').click();
    // Wait for portfolio view to load, then click on a product
    cy.get(this.navigation.productItem).first().click();
    return this;
  }

  navigateToAdGroupTargets() {
    this.navigateToProductDetail();
    // Click on an ad group to show targets, switches, and Add Target button
    cy.get(this.navigation.adGroupItem).first().click();
    return this;
  }

  // Form completion methods
  fillKeywordForm(keyword, bid, matchType = 'Exact') {
    this.typeKeyword(keyword).typeBid(bid).selectMatchType(matchType);
    return this;
  }

  fillProductForm(asin, bid) {
    this.typeAsin(asin).typeBid(bid);
    return this;
  }

  // Validation helpers
  submitAndExpectError(expectedError) {
    this.clickAddButton();
    this.assertErrorMessage(expectedError);
    return this;
  }

  submitAndExpectSuccess() {
    this.clickAddButton();
    cy.wait('@addTarget');
    this.assertModalIsClosed();
    return this;
  }

  // Switch component methods (for integration testing)
  assertSwitchExists() {
    cy.get(this.switch.container).should('be.visible');
    return this;
  }

  assertSwitchIsEnabled(index = 0) {
    this.getSwitchInTargetItem(index).should('be.checked');
    return this;
  }

  assertSwitchIsDisabled(index = 0) {
    this.getSwitchInTargetItem(index).should('not.be.checked');
    return this;
  }

  clickSwitch(index = 0) {
    this.getSwitchInTargetItem(index).click({ force: true });
    return this;
  }

  getTargetListItem(index = 0) {
    return cy.get(this.switch.targetListItem).eq(index);
  }

  getSwitchInTargetItem(index = 0) {
    return this.getTargetListItem(index).find(this.switch.input);
  }

  // Utility methods
  waitForModalToLoad() {
    this.assertModalIsOpen();
    // Wait for any initial loading to complete
    cy.get(this.modal.container).should('be.visible');
    return this;
  }

  assertFormFieldsVisible(targetType) {
    if (targetType === 'KEYWORD') {
      this.assertKeywordInputExists()
        .assertBidInputExists()
        .assertMatchTypeDropdownExists();
    } else if (targetType === 'PRODUCT') {
      this.assertAsinInputExists().assertBidInputExists();
    }
    return this;
  }

  // Test data helpers
  fillFormWithValidData(targetType, testData) {
    if (targetType === 'KEYWORD') {
      this.fillKeywordForm(testData.keyword, testData.bid, testData.matchType);
    } else if (targetType === 'PRODUCT') {
      this.fillProductForm(testData.asin, testData.bid);
    }
    return this;
  }

  // API interaction helpers
  verifyAddTargetApiCall(expectedData) {
    cy.wait('@addTarget').then((interception) => {
      Object.keys(expectedData).forEach((key) => {
        expect(interception.request.body).to.have.property(
          key,
          expectedData[key]
        );
      });
    });
    return this;
  }

  verifySwitchApiCall(expectedState) {
    cy.wait('@changeTargetStatus').then((interception) => {
      expect(interception.request.url).to.include(`state=${expectedState}`);
    });
    return this;
  }
}

export default AddTargetModalPage;
