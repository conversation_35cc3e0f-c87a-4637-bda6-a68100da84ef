"use client"

import { api } from "@/utils/api"
import { cn, formatDate, formatUTCDate } from "@/utils/msc"
import { useSession } from "next-auth/react"
import { useLocale, useTranslations, useFormatter } from 'next-intl'
import { forwardRef, Fragment, useEffect, useRef, useState } from "react"
import { CSVLink } from "react-csv"
import DatePicker, { registerLocale } from "react-datepicker"
import { ko } from "date-fns/locale"
import "react-datepicker/dist/react-datepicker.css";
import { Popover, PopoverButton, PopoverPanel, Tab, TabGroup, TabList, TabPanel, TabPanels, Transition } from '@headlessui/react'
import VendorPinnedColTable, { AdvertisedProduct } from "@/components/dashboard/vendor-pinned-col-table"
import { ProfileOption } from "@/components/dashboard/profile-select"
import { MarketplaceOption } from "@/components/dashboard/marketplace-select"
import AttributionWindowSelect, { attributionWindowOptions } from "@/components/dashboard/attribution-window-select"
import { PortfolioListItem } from "./ad-portfolio-layout-component"
import OptimizationSetSelect from "./optimization-set-select"
import ComparisonSelect, { comparisonOptions } from "@/components/dashboard/comparison-select"
import LegendSelect from "@/components/dashboard/vendor-legend-select"
import AdvancedFilterSelect from "./advanced-filter-select"
import { ArrowDownCircleIcon, CurrencyDollarIcon, PresentationChartLineIcon, ExclamationTriangleIcon, ArrowDownTrayIcon, QuestionMarkCircleIcon, XMarkIcon } from "@heroicons/react/20/solid"
import dynamic from "next/dynamic"
const VendorPlotGraph = dynamic(() => import('@/components/dashboard/vendor-plot-graph'), { ssr: false })
const VendorHourlyPlotGraph = dynamic(() => import('@/components/dashboard/vendor-hourly-plot-graph'), { ssr: false })
registerLocale('ko', ko)

interface DashboardLayoutProps {
    mopUserData: any;
    selectedProfile: ProfileOption;
    selectedMarketplace: MarketplaceOption;
}

export default function VendorReportsLayoutComponent({
    mopUserData,
    selectedProfile,
    selectedMarketplace
}: DashboardLayoutProps) {
  const t = useTranslations('DashboardPage')
  const locale = useLocale()
  const format = useFormatter()
  const { data: session, status } = useSession()
	const hourlyfetchCounter = useRef(0)
  const vendorFetchCounter = useRef(0)
  const dailyFetchCounter = useRef(0)
  const [mainProducts, setMainProducts] = useState<any[]>([])
  const [comparedProducts, setComparedProducts] = useState<any[]>([])
	const [mainProductsDailyData, setMainProductsDailyData] = useState<any[]>([])
	const [mainProductsHourlyData, setMainProductsHourlyData] = useState<any>({})
  const [comparedProductsDailyData, setComparedProductsDailyData] = useState<any[]>([])
  const [reportByAsin, setReportByAsin] = useState<any[]>([])
  const [reportByDate, setReportByDate] = useState<any[]>([])
	const [reportByHour, setReportByHour] = useState<any[]>([])
  const [comparedReportByDate, setComparedReportByDate] = useState<any[]>([])
  const [totalSalesReturned, setTotalSalesReturned] = useState(0)
	const [comparedTotalSalesReturned, setComparedTotalSalesReturned] = useState(0)
  const [totalRevenue, setTotalRevenue] = useState(0)
  const [comparedTotalRevenue, setComparedTotalRevenue] = useState(0)
	const [totalCogsRevenue, setTotalCogsRevenue] = useState(0)
  const [comparedTotalCogsRevenue, setComparedTotalCogsRevenue] = useState(0)
  const [totalFee, setTotalFee] = useState(0)
  const [comparedTotalFee, setComparedTotalFee] = useState(0)
  const [adSales, setAdSales] = useState(0)
  const [comparedAdSales, setComparedAdSales] = useState(0)
  const [adCost, setAdCost] = useState(0)
  const [comparedAdCost, setComparedAdCost] = useState(0)
  const [selectedAttributionWindow, setSelectedAttributionWindow] = useState(attributionWindowOptions[1])
  const [portfolioListItems, setPortfolioListItems] = useState<PortfolioListItem[]>([])
  const [selectedPortfolioItems, setSelectedPortfolioItems] = useState<PortfolioListItem[]>([])
  const [selectedComparison, setSelectedComparison] = useState(comparisonOptions[0])
  const [selectedGraphTabIndex, setSelectedGraphTabIndex] = useState(0)
    
  const formatCurrency = (value: number) => {
    return format.number(value, {
      style: 'currency',
      currency: selectedMarketplace?.default_currency_code || 'USD'
    })
  }

  // advanced filter options
  const advancedFilterOptions = [
    // low inventory
    {
      id: 1,
      name: t('filter.advancedFilter.content.low30d'),
      category: 'low-inventory',
      type: 'low30d',
    },
    {
      id: 2,
      name: t('filter.advancedFilter.content.low60d'),
      category: 'low-inventory',
      type: 'low60d',
    },
    {
      id: 3,
      name: t('filter.advancedFilter.content.low90d'),
      category: 'low-inventory',
      type: 'low90d',
    },
    // excess inventory
    {
      id: 4,
      name: t('filter.advancedFilter.content.excess180d'),
      category: 'excess-inventory',
      type: 'excess180d',
    },
    {
      id: 5,
      name: t('filter.advancedFilter.content.excess270d'),
      category: 'excess-inventory',
      type: 'excess270d',
    },
    {
      id: 6,
      name: t('filter.advancedFilter.content.excess365d'),
      category: 'excess-inventory',
      type: 'excess365d',
    },
    // recommended ASIN
    {
      id: 7,
      name: t('filter.advancedFilter.content.growth'),
      category: 'recommendation',
      type: 'GROWTH',
    },
    {
      id: 8,
      name: t('filter.advancedFilter.content.efficiency'),
      category: 'recommendation',
      type: 'EFFICIENCY',
    }
  ]
	// legend options
	const legendOptions = [
		// total performance
		{
			id: 1,
			name: t('graph.legendSelect.revenue'),
			category: 'total',
			type: 'revenue',
			color: '#a855f7',
		},
		{
			id: 2,
			name: t('graph.legendSelect.comparedRevenue'),
			category: 'total',
			type: 'compared-revenue',
			color: 'rgba(168, 85, 247, 0.8)',
		},
		{
			id: 3,
			name: t('graph.legendSelect.cogsRevenue'),
			category: 'total',
			type: 'cogs-revenue',
			color: '#6366f1',
		},
		{
			id: 4,
			name: t('graph.legendSelect.comparedCogsRevenue'),
			category: 'total',
			type: 'compared-cogs-revenue',
			color: 'rgba(99, 102, 241, 0.8)',
		},
		{
			id: 5,
			name: t('graph.legendSelect.glanceViews'),
			category: 'total',
			type: 'glance-views',
			color: '#f59e0b',
		},
		{
			id: 6,
			name: t('graph.legendSelect.comparedGlanceViews'),
			category: 'total',
			type: 'compared-glance-views',
			color: 'rgba(245, 158, 11, 0.8)',
		},
		// ad performance (sp)
		{
			id: 7,
			name: t('graph.legendSelect.adCost'),
			category: 'sp',
			type: 'ad-cost',
			color: '#1d4ed8',
		},
		{
			id: 8,
			name: t('graph.legendSelect.comparedAdCost'),
			category: 'sp',
			type: 'compared-ad-cost',
			color: 'rgba(29, 78, 216, 0.8)',
		},
		{
			id: 9,
			name: t('graph.legendSelect.adSales'),
			category: 'sp',
			type: 'ad-sales',
			color: '#6366f1',
		},
		{
			id: 10,
			name: t('graph.legendSelect.comparedAdSales'),
			category: 'sp',
			type: 'compared-ad-sales',
			color: 'rgba(99, 102, 241, 0.8)',
		},
		{
      id: 11,
      name: t('graph.legendSelect.adSalesSameSku'),
      category: 'sp',
      type: 'ad-sales-same-sku',
      color: '#66d9e8',
    },
    {
      id: 12,
      name: t('graph.legendSelect.comparedAdSalesSameSku'),
      category: 'sp',
      type: 'compared-ad-sales-same-sku',
      color: 'rgba(102, 217, 232, 0.8)',
    },
		{
			id: 13,
			name: t('graph.legendSelect.clicks'),
			category: 'sp',
			type: 'clicks',
			color: '#f97316',
		},
		{
			id: 14,
			name: t('graph.legendSelect.comparedClicks'),
			category: 'sp',
			type: 'compared-clicks',
			color: 'rgba(249, 115, 22, 0.8)',
		},
		{
      id: 15,
      name: t('graph.legendSelect.impressions'),
      category: 'sp',
      type: 'impressions',
      color: '#c53030',
    },
    {
      id: 16,
      name: t('graph.legendSelect.comparedImpressions'),
      category: 'sp',
      type: 'compared-impressions',
      color: 'rgba(197, 48, 48, 0.8)',
    },
		{
      id: 17,
      name: t('graph.legendSelect.roas'),
      category: 'sp',
      type: 'roas',
      color: '#22c55e',
    },
    {
      id: 18,
      name: t('graph.legendSelect.comparedRoas'),
      category: 'sp',
      type: 'compared-roas',
      color: 'rgba(34, 197, 94, 0.8)',
    },
		// ad performance (sd)
    {
      id: 19,
      name: t('graph.legendSelect.adCost'),
      category: 'sd',
      type: 'sd-cost',
      color: '#d8a71d',
    },
    {
      id: 20,
      name: t('graph.legendSelect.comparedAdCost'),
      category: 'sd',
      type: 'compared-sd-cost',
      color: 'rgba(216, 167, 29, 0.8)',
    },
    {
      id: 21,
      name: t('graph.legendSelect.adSales'),
      category: 'sd',
      type: 'sd-sales',
      color: '#f1ee63',
    },
    {
      id: 22,
      name: t('graph.legendSelect.comparedAdSales'),
      category: 'sd',
      type: 'compared-sd-sales',
      color: 'rgba(241, 238, 99, 0.8)',
    },
    {
      id: 23,
      name: t('graph.legendSelect.sdSalesPromotedClick'),
      category: 'sd',
      type: 'sd-sales-promoted-clicks',
      color: '#e87566',
    },
    {
      id: 24,
      name: t('graph.legendSelect.comparedSdSalesPromotedClick'),
      category: 'sd',
			type: 'compared-sd-sales-promoted-clicks',
      color: 'rgba(232, 117, 102, 0.8)',
    },
    {
      id: 25,
      name: t('graph.legendSelect.clicks'),
      category: 'sd',
      type: 'sd-clicks',
      color: '#169cf9',
    },
    {
      id: 26,
      name: t('graph.legendSelect.comparedClicks'),
      category: 'sd',
      type: 'compared-sd-clicks',
      color: 'rgba(22, 156, 249, 0.8)',
    },
    {
      id: 27,
      name: t('graph.legendSelect.impressions'),
      category: 'sd',
      type: 'sd-impressions',
      color: '#30c5c5',
    },
    {
      id: 28,
      name: t('graph.legendSelect.comparedImpressions'),
      category: 'sd',
      type: 'compared-sd-impressions',
      color: 'rgba(48, 197, 197, 0.8)',
    },
    {
      id: 29,
      name: t('graph.legendSelect.roas'),
      category: 'sd',
      type: 'sd-roas',
      color: '#c52289',
    },
    {
      id: 30,
      name: t('graph.legendSelect.comparedRoas'),
      category: 'sd',
      type: 'compared-sd-roas',
      color: 'rgba(197, 34, 137, 0.8)',
    },
	]
	// legend options for hourly graph
	const hourlyLegendOptions = [
		// total performance
		{
			id: 1,
			name: t('graph.legendSelect.revenue'),
			category: 'total',
			type: 'revenue',
			color: '#a855f7',
		},
		{
			id: 5,
			name: t('graph.legendSelect.glanceViews'),
			category: 'total',
			type: 'glance-views',
			color: '#f59e0b',
		},
		// ad performance (sp)
		{
			id: 7,
			name: t('graph.legendSelect.adCost'),
			category: 'sp',
			type: 'ad-cost',
			color: '#1d4ed8',
		},
		{
			id: 9,
			name: t('graph.legendSelect.adSales'),
			category: 'sp',
			type: 'ad-sales',
			color: '#6366f1',
		},
		{
      id: 11,
      name: t('graph.legendSelect.adSalesSameSku'),
      category: 'sp',
      type: 'ad-sales-same-sku',
      color: '#66d9e8',
    },
		{
			id: 13,
			name: t('graph.legendSelect.clicks'),
			category: 'sp',
			type: 'clicks',
			color: '#f97316',
		},
		{
      id: 15,
      name: t('graph.legendSelect.impressions'),
      category: 'sp',
      type: 'impressions',
      color: '#c53030',
    },
		// {
    //   id: 17,
    //   name: t('graph.legendSelect.roas'),
    //   category: 'sp',
    //   type: 'roas',
    //   color: '#22c55e',
    // },
		// ad performance (sd)
    {
      id: 19,
      name: t('graph.legendSelect.adCost'),
      category: 'sd',
      type: 'sd-cost',
      color: '#d8a71d',
    },
    {
      id: 21,
      name: t('graph.legendSelect.adSales'),
      category: 'sd',
      type: 'sd-sales',
      color: '#f1ee63',
    },
    {
      id: 23,
      name: t('graph.legendSelect.adSalesSameSku'),
      category: 'sd',
      type: 'sd-sales-same-sku',
      color: '#e87566',
    },
    {
      id: 25,
      name: t('graph.legendSelect.clicks'),
      category: 'sd',
      type: 'sd-clicks',
      color: '#169cf9',
    },
    {
      id: 27,
      name: t('graph.legendSelect.impressions'),
      category: 'sd',
      type: 'sd-impressions',
      color: '#30c5c5',
    },
    // {
    //   id: 29,
    //   name: t('graph.legendSelect.roas'),
    //   category: 'sd',
    //   type: 'sd-roas',
    //   color: '#c52289',
    // },
	]
	const [selectedAdvancedOptions, setSelectedAdvancedOptions] = useState<any>([])
  const [selectedLegend, setSelectedLegend] = useState([legendOptions[0], legendOptions[2], legendOptions[4], legendOptions[6]])
	const [selectedHourlyLegend, setSelectedHourlyLegend] = useState([hourlyLegendOptions[0], hourlyLegendOptions[1], hourlyLegendOptions[2], hourlyLegendOptions[3]])
  const [selectedProduct, setSelectedProduct] = useState<AdvertisedProduct | null>(null)
	const prevMonth = new Date();
  prevMonth.setMonth(prevMonth.getMonth() - 1);
  const [dateRange, setDateRange] = useState<Date[]>([
    new Date(prevMonth.getFullYear(), prevMonth.getMonth(), 1),
    new Date()
  ])
  const [startDate, endDate] = dateRange
  // endDate of comparedDateRange is determined by the time span between startDate and endDate
  const [comparedDateRange, setComparedDateRange] = useState<Date[]>([new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1), new Date()])
  const [comparedStartDate, comparedEndDate] = comparedDateRange
  const DateRangeInput = forwardRef(({ value, onClick }: { value: string, onClick: () => void }, ref) => (
    // @ts-ignore
    <button className="w-auto mt-1 rounded-lg bg-white shadow-md overflow-hidden cursor-pointer text-sm text-gray-600 focus:outline-none" onClick={onClick} ref={ref}>
      <div className="min-w-[200px] inline-flex py-2 px-4 bg-white hover:bg-gray-100/20">
        {value}
      </div>
    </button>
  ));
  DateRangeInput.displayName = 'DateRangeInput'
  
  const fetchProducts = async (targetStartDate: Date, targetEndDate: Date, signal: AbortSignal) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    if (targetStartDate === null || targetEndDate === null) {
      return
    }
    vendorFetchCounter.current += 1
		let joinedReportResponse = await api.getJoinedVendorData(selectedProfile.account_id, selectedMarketplace.marketplace_id, formatDate(targetStartDate), formatDate(targetEndDate), (session?.user as any).access_token, signal)
    vendorFetchCounter.current -= 1
    return {
			reportData: joinedReportResponse,
		}
  }
	const fetchProductsDailyData = async (targetStartDate: Date, targetEndDate: Date, targetAsin: string, optimizationSets: string, signal: AbortSignal) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    if (targetStartDate === null || targetEndDate === null) {
      return
    }
    dailyFetchCounter.current += 1
		let joinedDailyPerformanceResponse = await api.getJoinedVendorDailyPerformance(selectedProfile.account_id, selectedMarketplace.marketplace_id, formatDate(targetStartDate), formatDate(targetEndDate), targetAsin, optimizationSets, (session?.user as any).access_token, signal)
    dailyFetchCounter.current -= 1
    return {
			dailyData: joinedDailyPerformanceResponse
		}
  }
	const fetchProductsHourlyData = async (metrics: string, targetStartDate: Date, targetEndDate: Date, targetAsin: string, targetCampaignId: string, signal: AbortSignal) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    if (targetStartDate === null || targetEndDate === null) {
      return
    }
    hourlyfetchCounter.current += 1
		let joinedHourlyPerformanceResponse = await api.getHourlyReport(selectedProfile.account_id, selectedMarketplace.marketplace_id, metrics, formatDate(targetStartDate), formatDate(targetEndDate), targetAsin, targetCampaignId, (session?.user as any).access_token, signal)
    hourlyfetchCounter.current -= 1
    return {
			hourlyData: joinedHourlyPerformanceResponse
		}
  }
	const calculateDailyData = (dailyDataResponse: any) => {
		// graph related calculation
    let aggregatedReportByDate = [] as any[]
		for (const date in dailyDataResponse) {
			const targetItemIdx = aggregatedReportByDate.findIndex((i) => i.date === date)
			if (targetItemIdx === -1) {
				aggregatedReportByDate.push({
					date: date,
					// ad
					ad_sales: selectedAttributionWindow.type === '1d'
						? dailyDataResponse[date].ad?.sales1d || 0
						: selectedAttributionWindow.type === '7d'
							? dailyDataResponse[date].ad?.sales7d || 0
							: selectedAttributionWindow.type === '14d'
								? dailyDataResponse[date].ad?.sales14d || 0
								: dailyDataResponse[date].ad?.sales30d || 0,
					ad_sales_same_sku: selectedAttributionWindow.type === '1d'
						? dailyDataResponse[date].ad?.attributed_sales_same_sku1d || 0
						: selectedAttributionWindow.type === '7d'
							? dailyDataResponse[date].ad?.attributed_sales_same_sku7d || 0
							: selectedAttributionWindow.type === '14d'
								? dailyDataResponse[date].ad?.attributed_sales_same_sku14d || 0
								: dailyDataResponse[date].ad?.attributed_sales_same_sku30d || 0,
					ad_cost: dailyDataResponse[date].ad?.cost || 0,
					clicks: dailyDataResponse[date].ad?.clicks || 0,
					impressions: dailyDataResponse[date].ad?.impressions || 0,
					sd_cost: dailyDataResponse[date].ad?.sd_cost || 0,
          sd_sales: dailyDataResponse[date].ad?.sd_sales || 0,
          sd_sales_promoted_clicks: dailyDataResponse[date].ad?.sd_sales_promoted_clicks || 0,
          sd_clicks: dailyDataResponse[date].ad?.sd_clicks || 0,
          sd_impressions: dailyDataResponse[date].ad?.sd_impressions || 0,
					// glanceViews
					glance_views: dailyDataResponse[date].glanceViews || 0,
					// manufacturing
					manufacturing_customer_returns: dailyDataResponse[date].manufacturing?.customer_returns || 0,
					manufacturing_ordered_revenue_amount: dailyDataResponse[date].manufacturing?.ordered_revenue_amount || 0,
					manufacturing_ordered_units: dailyDataResponse[date].manufacturing?.ordered_units || 0,
					manufacturing_shipped_cogs_amount: dailyDataResponse[date].manufacturing?.shipped_cogs_amount || 0,
					manufacturing_shipped_revenue_amount: dailyDataResponse[date].manufacturing?.shipped_revenue_amount || 0,
					manufacturing_shipped_units: dailyDataResponse[date].manufacturing?.shipped_units || 0,
					// sourcing
					sourcing_customer_returns: dailyDataResponse[date].sourcing?.customer_returns || 0,
					sourcing_shipped_cogs_amount: dailyDataResponse[date].sourcing?.shipped_cogs_amount || 0,
					sourcing_shipped_revenue_amount: dailyDataResponse[date].sourcing?.shipped_revenue_amount || 0,
					sourcing_shipped_units: dailyDataResponse[date].sourcing?.shipped_units || 0,
				})
			} else {
				// ad
				let targetSales = selectedAttributionWindow.type === '1d'
					? dailyDataResponse[date].ad?.sales1d || 0
					: selectedAttributionWindow.type === '7d'
						? dailyDataResponse[date].ad?.sales7d || 0
						: selectedAttributionWindow.type === '14d'
							? dailyDataResponse[date].ad?.sales14d || 0
							: dailyDataResponse[date].ad?.sales30d || 0
				aggregatedReportByDate[targetItemIdx].ad_sales = aggregatedReportByDate[targetItemIdx].ad_sales + targetSales
				let targetSalesSameSku = selectedAttributionWindow.type === '1d'
					? dailyDataResponse[date].ad?.attributed_sales_same_sku1d || 0
					: selectedAttributionWindow.type === '7d'
						? dailyDataResponse[date].ad?.attributed_sales_same_sku7d || 0
						: selectedAttributionWindow.type === '14d'
							? dailyDataResponse[date].ad?.attributed_sales_same_sku14d || 0
							: dailyDataResponse[date].ad?.attributed_sales_same_sku30d || 0
				aggregatedReportByDate[targetItemIdx].ad_sales_same_sku = aggregatedReportByDate[targetItemIdx].ad_sales_same_sku + targetSalesSameSku
				aggregatedReportByDate[targetItemIdx].ad_cost = aggregatedReportByDate[targetItemIdx].ad_cost + (dailyDataResponse[date].ad?.cost || 0)
				aggregatedReportByDate[targetItemIdx].clicks = aggregatedReportByDate[targetItemIdx].clicks + (dailyDataResponse[date].ad?.clicks || 0)
				aggregatedReportByDate[targetItemIdx].impressions = aggregatedReportByDate[targetItemIdx].impressions + (dailyDataResponse[date].ad?.impressions || 0)
				aggregatedReportByDate[targetItemIdx].sd_cost = aggregatedReportByDate[targetItemIdx].sd_cost + (dailyDataResponse[date].sd_cost || 0)
        aggregatedReportByDate[targetItemIdx].sd_sales = aggregatedReportByDate[targetItemIdx].sd_sales + (dailyDataResponse[date].sd_sales || 0)
        aggregatedReportByDate[targetItemIdx].sd_sales_promoted_clicks = aggregatedReportByDate[targetItemIdx].sd_sales_promoted_clicks + (dailyDataResponse[date].sd_sales_promoted_clicks || 0)
        aggregatedReportByDate[targetItemIdx].sd_clicks = aggregatedReportByDate[targetItemIdx].sd_clicks + (dailyDataResponse[date].sd_clicks || 0)
        aggregatedReportByDate[targetItemIdx].sd_impressions = aggregatedReportByDate[targetItemIdx].sd_impressions + (dailyDataResponse[date].sd_impressions || 0)
				// glanceViews
				aggregatedReportByDate[targetItemIdx].glance_views = aggregatedReportByDate[targetItemIdx].glance_views + (dailyDataResponse[date].glanceViews || 0)
				// manufacturing
				aggregatedReportByDate[targetItemIdx].manufacturing_customer_returns = aggregatedReportByDate[targetItemIdx].manufacturing_customer_returns + (dailyDataResponse[date].manufacturing?.customer_returns || 0)
				aggregatedReportByDate[targetItemIdx].manufacturing_ordered_revenue_amount = aggregatedReportByDate[targetItemIdx].manufacturing_ordered_revenue_amount + (dailyDataResponse[date].manufacturing?.ordered_revenue_amount || 0)
				aggregatedReportByDate[targetItemIdx].manufacturing_ordered_units = aggregatedReportByDate[targetItemIdx].manufacturing_ordered_units + (dailyDataResponse[date].manufacturing?.ordered_units || 0)
				aggregatedReportByDate[targetItemIdx].manufacturing_shipped_cogs_amount = aggregatedReportByDate[targetItemIdx].manufacturing_shipped_cogs_amount + (dailyDataResponse[date].manufacturing?.shipped_cogs_amount || 0)
				aggregatedReportByDate[targetItemIdx].manufacturing_shipped_revenue_amount = aggregatedReportByDate[targetItemIdx].manufacturing_shipped_revenue_amount + (dailyDataResponse[date].manufacturing?.shipped_revenue_amount || 0)
				aggregatedReportByDate[targetItemIdx].manufacturing_shipped_units = aggregatedReportByDate[targetItemIdx].manufacturing_shipped_units + (dailyDataResponse[date].manufacturing?.shipped_units || 0)
				// sourcing
				aggregatedReportByDate[targetItemIdx].sourcing_customer_returns = aggregatedReportByDate[targetItemIdx].sourcing_customer_returns + (dailyDataResponse[date].sourcing?.customer_returns || 0)
				aggregatedReportByDate[targetItemIdx].sourcing_shipped_cogs_amount = aggregatedReportByDate[targetItemIdx].sourcing_shipped_cogs_amount + (dailyDataResponse[date].sourcing?.shipped_cogs_amount || 0)
				aggregatedReportByDate[targetItemIdx].sourcing_shipped_revenue_amount = aggregatedReportByDate[targetItemIdx].sourcing_shipped_revenue_amount + (dailyDataResponse[date].sourcing?.shipped_revenue_amount || 0)
				aggregatedReportByDate[targetItemIdx].sourcing_shipped_units = aggregatedReportByDate[targetItemIdx].sourcing_shipped_units + (dailyDataResponse[date].sourcing?.shipped_units || 0)
			}
		}
    aggregatedReportByDate = aggregatedReportByDate.map((report: any) => {
      return {
        ...report,
				roas: report.ad_cost === 0
          ? 0
          : ((report.ad_sales / report.ad_cost) * 100).toFixed(2),
				sd_roas: report.sd_cost === 0
          ? 0
          : ((report.sd_sales / report.sd_cost) * 100).toFixed(2),
        // estimated_margin: report.shipped_cogs_amount === 0
        //   ? 0
        //   : (((report.shipped_cogs_amount - report.total_fee - report.ad_cost) / report.shipped_cogs_amount) * 100).toFixed(2),
				// margin = cogs revenue(from sourcing view) - ad cost - (promotion + coupon cost)
        date: report.date
      }
    }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    let filledAggregatedReportByDate = [] as any[]
		const firstDate = new Date(aggregatedReportByDate[0]?.date)
    const lastDate = new Date(aggregatedReportByDate[aggregatedReportByDate.length - 1]?.date)
    const gapInDays = firstDate && lastDate
      ? Math.floor((lastDate.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24))
      : 0
    if (firstDate && lastDate) {
      let currentDate = new Date(firstDate)
      while (currentDate <= lastDate) {
				const existingReport = aggregatedReportByDate.find((report: any) => formatUTCDate(report.date, "-") === formatUTCDate(currentDate, "-"))
        if (existingReport) {
					filledAggregatedReportByDate.push(
						{
							...existingReport,
							date: new Date(existingReport.date)
						}
					)
        } else {
          filledAggregatedReportByDate.push({
            date: new Date(currentDate),
						ad_sales: 0,
						ad_sales_same_sku: 0,
						ad_cost: 0,
						clicks: 0,
						impressions: 0,
						sd_cost: 0,
            sd_sales: 0,
            sd_sales_promoted_clicks: 0,
            sd_clicks: 0,
            sd_impressions: 0,
            sd_roas: 0,
						glance_views: 0,
						manufacturing_customer_returns: 0,
						manufacturing_ordered_revenue_amount: 0,
						manufacturing_ordered_units: 0,
            manufacturing_shipped_cogs_amount: 0,
            manufacturing_shipped_revenue_amount: 0,
            manufacturing_shipped_units: 0,
						sourcing_customer_returns: 0,
						sourcing_shipped_cogs_amount: 0,
						sourcing_shipped_revenue_amount: 0,
						sourcing_shipped_units: 0,
          })
        }
        currentDate.setDate(currentDate.getDate() + 1)
      }
    }
		return filledAggregatedReportByDate
	}
	const getMetricsForHourlyGraph = () => {
    return selectedHourlyLegend.reduce((acc, curr) => {
      if (curr.category === 'total') {
        if (curr.type === 'revenue') {
          return acc + ',' + 'sales_vendor'
        } else if (curr.type === 'glance-views') {
          return acc + ',' + 'page_views_vendor'
        }
      } else if (curr.category === 'sp') {
        if (curr.type === 'ad-cost') {
          return acc + ',' + 'sp_ad_spend'
        } else if (curr.type === 'clicks') {
          return acc + ',' + 'sp_ad_clicks'
        } else if (curr.type === 'impressions') {
          return acc + ',' + 'sp_ad_impressions'
        } else if (curr.type === 'ad-sales') {
          return acc + ',' + 'sp_ad_sales_' + selectedAttributionWindow.type
        } else if (curr.type === 'ad-sales-same-sku') {
          return acc + ',' + 'sp_ad_sales_' + selectedAttributionWindow.type + '_same_sku'
        }
      } else if (curr.category === 'sd') {
        if (curr.type === 'sd-cost') {
          return acc + ',' + 'sd_ad_spend'
        } else if (curr.type === 'sd-clicks') {
          return acc + ',' + 'sd_ad_clicks'
        } else if (curr.type === 'sd-impressions') {
          return acc + ',' + 'sd_ad_impressions'
        } else if (curr.type === 'sd-sales') {  
          return acc + ',' + 'sd_ad_sales_' + selectedAttributionWindow.type
        } else if (curr.type === 'sd-sales-same-sku') {
          return acc + ',' + 'sd_ad_sales_' + selectedAttributionWindow.type + '_same_sku'
        }
      }
      return acc + ',' + curr.type
    }, '').slice(1)
  }
    
  const calculateHourlyData = (hourlyDataResponse: any) => {
    const keyToLegend = (key: string) => {
      if (key === 'sales_vendor') {
        return 'revenue'
      } else if (key === 'page_views_vendor') {
        return 'glance_views'
      } else if (key === 'sp_ad_spend') {
        return 'ad_cost'
      } else if (key === 'sp_ad_sales_' + selectedAttributionWindow.type) {
        return 'ad_sales'
      } else if (key === 'sp_ad_sales_' + selectedAttributionWindow.type + '_same_sku') {
        return 'ad_sales_same_sku'
      } else if (key === 'sp_ad_clicks') {
        return 'clicks'
      } else if (key === 'sp_ad_impressions') {
        return 'impressions'
      } else if (key === 'sd_ad_spend') {
        return 'sd_cost'
      } else if (key === 'sd_ad_sales_' + selectedAttributionWindow.type) {
        return 'sd_sales'
      } else if (key === 'sd_ad_sales_' + selectedAttributionWindow.type + '_same_sku') {
        return 'sd_sales_same_sku'
      } else if (key === 'sd_ad_clicks') {
        return 'sd_clicks'
      } else if (key === 'sd_ad_impressions') {
        return 'sd_impressions'
      }
    }
    // graph related calculation
    let aggregatedReportByHour = [] as any[]
    for (const key in hourlyDataResponse) {
      // hourlyDataResponse[key] is an object with hour as key
      Object.keys(hourlyDataResponse[key]).forEach((hour) => {
        // Find or create the entry for this hour
        let entry = aggregatedReportByHour.find((item: any) => item.hour === Number(hour));
        if (!entry) {
          entry = { hour: Number(hour) };
          aggregatedReportByHour.push(entry);
        }
        const legendKey = keyToLegend(key);
        if (legendKey) {
          entry[legendKey] = hourlyDataResponse[key][hour];
        }
      });
    }
    return aggregatedReportByHour
  }
  const calculateAggregatedReport = (filteredReportResponse: any) => {
    // table related calculation
    let aggregatedReportByAsin = [] as any[]
    filteredReportResponse.map((report: any) => {
      // product
      report.shipping_price = report.shipping_price || 0
			report.listing_price = report.listing_price || 0
			report.lowest_price = report.listing_price + report.shipping_price
      report.eligibility_status = report.eligibility_status || ''
      report.fulfillment_channel = report.fulfillment_channel || ''
			report.open_purchase_order_units = report.open_purchase_order_units || 0
      report.available_quantity = report.available_quantity
      report.unfulfillable_quantity = report.unfulfillable_quantity
      report.reserved_quantity = report.reserved_quantity
      report.inbound_quantity = report.inbound_quantity
			report.estimated_fee = report.estimated_fee || 0
      report.points = report.points
      // vp
      report.shipped_revenue_amount = report.vp_performance.sourcing?.shipped_revenue_amount || 0
			report.shipped_cogs_amount = report.vp_performance.sourcing?.shipped_cogs_amount || 0
      report.shipped_units = report.vp_performance.sourcing?.shipped_units || 0
			report.return_quantity = report.vp_performance.sourcing?.customer_returns || 0
      report.return_sales = report.shipped_units > 0
				? report.return_quantity * (report.shipped_cogs_amount / report.shipped_units)
				: 0
      console.log(report.vp_performance.estimated_fee)
			report.glance_views = report.vp_performance.glanceViews || 0
			report.total_fee = report.vp_performance.estimated_fee || 0
      // ads (sp)
      report.ad_sales = selectedAttributionWindow.type === '1d'
        ? report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sales1d || 0), 0)
        : selectedAttributionWindow.type === '7d'
          ? report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sales7d || 0), 0)
          : selectedAttributionWindow.type === '14d'
            ? report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sales14d || 0), 0)
            : report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sales30d || 0), 0)
      report.units_sold_clicks = selectedAttributionWindow.type === '1d'
        ? report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.units_sold_clicks1d || 0), 0)
        : selectedAttributionWindow.type === '7d'
          ? report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.units_sold_clicks7d || 0), 0)
          : selectedAttributionWindow.type === '14d'
            ? report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.units_sold_clicks14d || 0), 0)
            : report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.units_sold_clicks30d || 0), 0)
			report.attributed_sales_same_sku = selectedAttributionWindow.type === '1d'
        ? report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.attributed_sales_same_sku1d || 0), 0)
        : selectedAttributionWindow.type === '7d'
          ? report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.attributed_sales_same_sku7d || 0), 0)
          : selectedAttributionWindow.type === '14d'
            ? report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.attributed_sales_same_sku14d || 0), 0)
            : report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.attributed_sales_same_sku30d || 0), 0)
      report.units_sold_same_sku = selectedAttributionWindow.type === '1d'
        ? report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.units_sold_same_sku1d || 0), 0)
        : selectedAttributionWindow.type === '7d'
          ? report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.units_sold_same_sku7d || 0), 0)
          : selectedAttributionWindow.type === '14d'
            ? report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.units_sold_same_sku14d || 0), 0)
            : report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.units_sold_same_sku30d || 0), 0)
        
      report.cost = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.cost || 0), 0)
      report.impressions = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.impressions || 0), 0)
      report.clicks = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.clicks || 0), 0)
			// ads (sd)
      report.sd_sales = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sd_sales || 0), 0)
      report.sd_sales_promoted_clicks = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sd_sales_promoted_clicks || 0), 0)
      report.sd_cost = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sd_cost || 0), 0)
      report.sd_units_sold = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sd_units_sold || 0), 0)
      report.sd_clicks = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sd_clicks || 0), 0)
      report.sd_impressions = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sd_impressions || 0), 0)
      report.sd_impressions_views = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sd_impressions_views || 0), 0)
      report.sd_cumulative_reach = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sd_cumulative_reach || 0), 0)
      report.sd_detail_page_views = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sd_detail_page_views || 0), 0)
      report.sd_new_to_brand_detail_page_views = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sd_new_to_brand_detail_page_views || 0), 0)
      report.sd_new_to_brand_sales = report.ad_performance.reduce((sum: number, ad: any) => sum + (ad.sd_new_to_brand_sales || 0), 0)
      aggregatedReportByAsin.push(report)
    })
    // sp
    let tempTotalRevenue = 0
		let tempTotalCogsRevenue = 0
    let tempTotalReturnSales = 0
    let tempTotalFee = 0
    // ads
    let tempAdSales = 0
    let tempAdUnitsSold = 0
    let tempAdCost = 0
    aggregatedReportByAsin.map((report: any) => {
      tempTotalRevenue = tempTotalRevenue + report.shipped_revenue_amount
			tempTotalCogsRevenue = tempTotalCogsRevenue + report.shipped_cogs_amount
      tempTotalReturnSales = tempTotalReturnSales + report.return_sales
      tempTotalFee = tempTotalFee + report.total_fee
			tempAdSales = tempAdSales + report.ad_sales + report.sd_sales
      tempAdUnitsSold = tempAdUnitsSold + report.units_sold_clicks
      tempAdCost = tempAdCost + report.cost + report.sd_cost
      
      // sp
			report.acos = report.ad_sales === 0
        ? '-'
        : (parseFloat(report.cost) / parseFloat(report.ad_sales)).toFixed(4)
      report.sp_conv_rate = report.glance_views === 0
        ? '-'
        : (parseInt(report.shipped_units) / parseInt(report.glance_views)).toFixed(4)
      report.click_through_rate = report.impressions === 0
        ? '-'
        : (parseInt(report.clicks) / parseInt(report.impressions)).toFixed(4)
      report.cost_per_click = report.clicks === 0
        ? '-'
        : (parseFloat(report.cost) / parseInt(report.clicks)).toFixed(4)
      report.ad_conv_rate = report.clicks === 0
        ? '-'
        : (parseInt(report.units_sold_clicks) / parseInt(report.clicks)).toFixed(4)
			report.profit = (parseFloat(report.shipped_cogs_amount) - parseFloat(report.total_fee) - parseFloat(report.cost) - parseFloat(report.sd_cost)).toFixed(4)
			report.tacos = report.shipped_cogs_amount === 0
        ? '-'
        : (parseFloat((report.cost + report.sd_cost)) / parseFloat(report.shipped_cogs_amount)).toFixed(4)

			// total ads
      report.total_ad_sales = report.ad_sales + report.sd_sales
      report.total_ad_cost = report.cost + report.sd_cost
      report.roas = (report.cost + report.sd_cost) === 0
        ? '-'
        : (parseFloat((report.ad_sales + report.sd_sales)) / parseFloat((report.cost + report.sd_cost))).toFixed(4)

			// sp
      report.sp_roas = report.cost === 0
        ? '-'
        : (parseFloat(report.ad_sales) / parseFloat(report.cost)).toFixed(4)
			
			// sd
      report.sd_roas = report.sd_cost === 0
        ? '-'
        : (parseFloat(report.sd_sales) / parseFloat(report.sd_cost)).toFixed(4)
      report.sd_acos = report.sd_sales === 0
        ? '-'
        : (parseFloat(report.sd_cost) / parseFloat(report.sd_sales)).toFixed(4)
      report.sd_conv_rate = report.sd_clicks === 0
        ? '-'
        : (parseInt(report.sd_units_sold) / parseInt(report.sd_clicks)).toFixed(4)
      report.sd_view_click_through_rate = report.sd_impressions_views === 0
        ? '-'
        : (parseInt(report.sd_clicks) / parseInt(report.sd_impressions_views)).toFixed(4)
      report.sd_new_to_brand_sales_rate = report.sd_sales === 0
        ? '-'
        : (parseFloat(report.sd_new_to_brand_sales) / parseFloat(report.sd_sales)).toFixed(4)
      report.sd_impression_frequency_average = report.sd_cumulative_reach === 0
        ? '-'
        : (parseInt(report.sd_impressions) / parseInt(report.sd_cumulative_reach)).toFixed(4)
      report.sd_viewability_rate = report.sd_impressions === 0
        ? '-'
        : (parseInt(report.sd_impressions_views) / parseInt(report.sd_impressions)).toFixed(4)
      report.sd_new_to_brand_detail_page_view_rate = report.sd_impressions === 0
        ? '-'
        : (parseInt(report.sd_new_to_brand_detail_page_views) / parseInt(report.sd_impressions)).toFixed(4)
    })
    return {
			aggregatedReportByAsin: tempTotalCogsRevenue === 0
        ? aggregatedReportByAsin.sort((a, b) => b.ad_sales - a.ad_sales)
        : aggregatedReportByAsin.sort((a, b) => b.shipped_cogs_amount - a.shipped_cogs_amount),
      totalRevenue: tempTotalRevenue,
			totalCogsRevenue: tempTotalCogsRevenue,
      totalReturnSales: tempTotalReturnSales,
      totalFee: tempTotalFee,
      adSales: tempAdSales,
      adUnitsSold: tempAdUnitsSold,
      adCost: tempAdCost
    }
  }
  const fetchMainProductsInfo = async (targetStartDate: Date, targetEndDate: Date, signal: AbortSignal) => {
    const data = await fetchProducts(targetStartDate, targetEndDate, signal)
    data?.reportData && data?.reportData.length && setMainProducts(data?.reportData || [])
  }
	const fetchMainProductsDailyInfo = async (targetStartDate: Date, targetEndDate: Date, targetAsin: string, optimizationSets: string,  signal: AbortSignal) => {
    const data = await fetchProductsDailyData(targetStartDate, targetEndDate, targetAsin, optimizationSets, signal)
		data?.dailyData && setMainProductsDailyData(data?.dailyData || [])
  }
	const fetchMainProductsHourlyInfo = async (metrics: string, targetStartDate: Date, targetEndDate: Date, targetAsin: string, targetCampaignId: string,  signal: AbortSignal) => {
    const data = await fetchProductsHourlyData(metrics, targetStartDate, targetEndDate, targetAsin, targetCampaignId, signal)
		data?.hourlyData && setMainProductsHourlyData(data?.hourlyData || {})
  }
  const fetchComparedProductsInfo = async (targetStartDate: Date, targetEndDate: Date, signal: AbortSignal) => {
    const data = await fetchProducts(targetStartDate, targetEndDate, signal)
    data?.reportData && data?.reportData.length && setComparedProducts(data?.reportData || [])
  }
	const fetchComparedProductsDailyInfo = async (targetStartDate: Date, targetEndDate: Date, targetAsin: string, optimizationSets: string, signal: AbortSignal) => {
    const data = await fetchProductsDailyData(targetStartDate, targetEndDate, targetAsin, optimizationSets, signal)
		data?.dailyData && setComparedProductsDailyData(data?.dailyData || [])
  }
  const fetchOptimizationSets = async (signal: AbortSignal) => {
    if (!(session?.user as any).access_token) {
      console.log('access token is missing in the session.')
    }
    let optimizationSetsResponse = await api.getListOptimizationsForReport(selectedProfile.account_id, selectedMarketplace.marketplace_id, (session?.user as any).access_token, signal)
    setPortfolioListItems(optimizationSetsResponse || [])
  }
  // calculate aggregated report onMainProductsChange
  useEffect(() => {
    let filteredReportResponse = selectedProduct
      ? mainProducts.filter((report: any) => report.asin === selectedProduct.asin)
      : mainProducts
    if (selectedPortfolioItems.length > 0) {
      filteredReportResponse = filteredReportResponse.filter((report: any) => selectedPortfolioItems.some(portfolio => portfolio.target_products?.some((item) => item.asin === report.asin)))
    }
		if (selectedAdvancedOptions.length > 0) {
			const lowInventory = selectedAdvancedOptions.find((option: any) => option.category === 'low-inventory')
      const excessInventory = selectedAdvancedOptions.find((option: any) => option.category === 'excess-inventory')
      const recommendation = selectedAdvancedOptions.find((option: any) => option.category === 'recommendation')
			if (lowInventory) {
        if (lowInventory.type === "low30d") {
          filteredReportResponse = filteredReportResponse.filter((report: any) => {
            return report.estimated_days_of_supply && typeof report.estimated_days_of_supply === "number" && report.estimated_days_of_supply <= 30
          })
        } else if (lowInventory.type === "low60d") {
          filteredReportResponse = filteredReportResponse.filter((report: any) => {
            return report.estimated_days_of_supply && typeof report.estimated_days_of_supply === "number" && report.estimated_days_of_supply <= 60
          })
        } else if (lowInventory.type === "low90d") {
          filteredReportResponse = filteredReportResponse.filter((report: any) => {
            return report.estimated_days_of_supply && typeof report.estimated_days_of_supply === "number" && report.estimated_days_of_supply <= 90
          })
        }
      }
      if (excessInventory) {
        if (excessInventory.type === "excess180d") {
          filteredReportResponse = filteredReportResponse.filter((report: any) => {
            return report.estimated_days_of_supply && typeof report.estimated_days_of_supply === "number" && report.estimated_days_of_supply >= 180
          })
        } else if (excessInventory.type === "excess270d") {
          filteredReportResponse = filteredReportResponse.filter((report: any) => {
            return report.estimated_days_of_supply && typeof report.estimated_days_of_supply === "number" && report.estimated_days_of_supply >= 270
          })
        } else if (excessInventory.type === "excess365d") {
          filteredReportResponse = filteredReportResponse.filter((report: any) => {
            return report.estimated_days_of_supply && typeof report.estimated_days_of_supply === "number" && report.estimated_days_of_supply >= 365
          })
        }
      }
      if (recommendation) {
        filteredReportResponse = filteredReportResponse.filter((report: any) => report.recommend_type === recommendation.type )
      }
    }

		const reportData = calculateAggregatedReport(filteredReportResponse || [])
    if (reportData) {
      // vp metric
      setTotalRevenue(reportData.totalRevenue)
			setTotalCogsRevenue(reportData.totalCogsRevenue)
      setTotalSalesReturned(reportData.totalReturnSales)
      setTotalFee(reportData.totalFee)
      // ads metric
      setAdSales(reportData.adSales)
      setAdCost(reportData.adCost)
      // table
      setReportByAsin(reportData.aggregatedReportByAsin)
    }
  }, [mainProducts, selectedProduct, selectedAttributionWindow, selectedPortfolioItems, selectedAdvancedOptions])
	// calculate daily data onMainProductsChange
	useEffect(() => {
		const dailyData = calculateDailyData(mainProductsDailyData || [])
		if (dailyData) {
      setReportByDate(dailyData)
		}
	}, [mainProductsDailyData])
	// calculate hourly data onMainProductsChange
	useEffect(() => {
		const hourlyData = calculateHourlyData(mainProductsHourlyData || {})
    if (hourlyData && Object.keys(hourlyData).length > 0) {
      setReportByHour(hourlyData)
    }
	}, [mainProductsHourlyData])
  // calculate aggregated report onComparedProductsChange
  useEffect(() => {
    let filteredReportResponse = selectedProduct
      ? comparedProducts.filter((report: any) => report.asin === selectedProduct.asin)
      : comparedProducts
    if (selectedPortfolioItems.length > 0) {
      filteredReportResponse = filteredReportResponse.filter((report: any) => selectedPortfolioItems.some(portfolio => portfolio.target_products?.some((item) => item.asin === report.asin)))
    }
		if (selectedAdvancedOptions.length > 0) {
			const lowInventory = selectedAdvancedOptions.find((option: any) => option.category === 'low-inventory')
      const excessInventory = selectedAdvancedOptions.find((option: any) => option.category === 'excess-inventory')
      const recommendation = selectedAdvancedOptions.find((option: any) => option.category === 'recommendation')
			if (lowInventory) {
        if (lowInventory.type === "low30d") {
          filteredReportResponse = filteredReportResponse.filter((report: any) => {
            return report.estimated_days_of_supply && typeof report.estimated_days_of_supply === "number" && report.estimated_days_of_supply <= 30
          })
        } else if (lowInventory.type === "low60d") {
          filteredReportResponse = filteredReportResponse.filter((report: any) => {
            return report.estimated_days_of_supply && typeof report.estimated_days_of_supply === "number" && report.estimated_days_of_supply <= 60
          })
        } else if (lowInventory.type === "low90d") {
          filteredReportResponse = filteredReportResponse.filter((report: any) => {
            return report.estimated_days_of_supply && typeof report.estimated_days_of_supply === "number" && report.estimated_days_of_supply <= 90
          })
        }
      }
      if (excessInventory) {
        if (excessInventory.type === "excess180d") {
          filteredReportResponse = filteredReportResponse.filter((report: any) => {
            return report.estimated_days_of_supply && typeof report.estimated_days_of_supply === "number" && report.estimated_days_of_supply >= 180
          })
        } else if (excessInventory.type === "excess270d") {
          filteredReportResponse = filteredReportResponse.filter((report: any) => {
            return report.estimated_days_of_supply && typeof report.estimated_days_of_supply === "number" && report.estimated_days_of_supply >= 270
          })
        } else if (excessInventory.type === "excess365d") {
          filteredReportResponse = filteredReportResponse.filter((report: any) => {
            return report.estimated_days_of_supply && typeof report.estimated_days_of_supply === "number" && report.estimated_days_of_supply >= 365
          })
        }
      }
      if (recommendation) {
        filteredReportResponse = filteredReportResponse.filter((report: any) => report.recommend_type === recommendation.type )
      }
    }

		const reportData = calculateAggregatedReport(filteredReportResponse || [])
    if (reportData) {
      // vp metric
			setComparedTotalRevenue(reportData.totalRevenue)
			setComparedTotalCogsRevenue(reportData.totalCogsRevenue)
			setComparedTotalSalesReturned(reportData.totalReturnSales)
      setComparedTotalFee(reportData.totalFee)
      // ads metric
      setComparedAdSales(reportData.adSales)
      setComparedAdCost(reportData.adCost)
      // table
      // setReportByAsin(data.aggregatedReportByAsin)
    }
  }, [comparedProducts, selectedProduct, selectedAttributionWindow, selectedPortfolioItems, selectedAdvancedOptions])
	// calculate daily data onComparedProductsChange
	useEffect(() => {
		const dailyData = calculateDailyData(comparedProductsDailyData || [])
		if (dailyData) {
			setComparedReportByDate(dailyData)
		}
	}, [comparedProductsDailyData])
	// fetch daily data when selectedProduct, selectedPortfolioItems change
	useEffect(() => {
		const targetAsin = selectedProduct
      ? selectedProduct.asin
      : ""
		const optimizationSets = selectedPortfolioItems.length > 0
			? selectedPortfolioItems.map((portfolio) => portfolio.id).join(',')
			: ""
		if (startDate && endDate) {
			const abortController = new AbortController()
      if (selectedComparison.type === 'month') {
        const previousMonthStartDate = new Date(startDate)
        previousMonthStartDate.setMonth(previousMonthStartDate.getMonth() - 1)
        const previousMonthEndDate = new Date(endDate)
        previousMonthEndDate.setMonth(previousMonthEndDate.getMonth() - 1)
        mopUserData && fetchComparedProductsDailyInfo(previousMonthStartDate, previousMonthEndDate, targetAsin, optimizationSets, abortController.signal)
      } else if (selectedComparison.type === 'year') {
        const previousYearStartDate = new Date(startDate)
        previousYearStartDate.setFullYear(previousYearStartDate.getFullYear() - 1)
        const previousYearEndDate = new Date(endDate)
        previousYearEndDate.setFullYear(previousYearEndDate.getFullYear() - 1)
        mopUserData && fetchComparedProductsDailyInfo(previousYearStartDate, previousYearEndDate, targetAsin, optimizationSets, abortController.signal)
      } else if (selectedComparison.type === 'custom') {
				mopUserData && fetchComparedProductsDailyInfo(comparedStartDate, comparedEndDate, targetAsin, optimizationSets, abortController.signal)
      }
			
			if (selectedGraphTabIndex === 0) {
        mopUserData && selectedMarketplace && fetchMainProductsDailyInfo(startDate, endDate, targetAsin, optimizationSets, abortController.signal)
      } else if (selectedGraphTabIndex === 1) {
        const metrics = getMetricsForHourlyGraph()
        mopUserData && selectedMarketplace && fetchMainProductsHourlyInfo(
          metrics,
          startDate,
          endDate,
          targetAsin,
          "",
          abortController.signal
        )
      }
			return () => {
				abortController.abort()
			}
    }
	}, [startDate, endDate, selectedProduct, selectedAttributionWindow, selectedAdvancedOptions, selectedPortfolioItems, selectedGraphTabIndex, selectedComparison, comparedStartDate, comparedEndDate, selectedMarketplace, mopUserData])
	useEffect(() => {
    if (selectedGraphTabIndex !== 1) return;
    if  (startDate && endDate) {
      const targetAsin = selectedProduct
        ? selectedProduct.asin
        : ""
      const metrics = getMetricsForHourlyGraph()
      const abortController = new AbortController()
      mopUserData && selectedMarketplace && fetchMainProductsHourlyInfo(
        metrics,
        startDate,
        endDate,
        targetAsin,
        "",
        abortController.signal
      )
      return () => {
        abortController.abort()
      }
    }
  }, [selectedHourlyLegend, selectedGraphTabIndex, startDate, endDate, selectedMarketplace, mopUserData, selectedProduct])

  // fetch advertisedProducts onMopUserDataChange or onMarketplaceChange or onDateRangeChange
  useEffect(() => {
    if (startDate && endDate) {
			const abortController = new AbortController()
      if (selectedComparison.type === 'month') {
        const previousMonthStartDate = new Date(startDate)
        previousMonthStartDate.setMonth(previousMonthStartDate.getMonth() - 1)
        const previousMonthEndDate = new Date(endDate)
        previousMonthEndDate.setMonth(previousMonthEndDate.getMonth() - 1)
        mopUserData && fetchComparedProductsInfo(previousMonthStartDate, previousMonthEndDate, abortController.signal)
      } else if (selectedComparison.type === 'year') {
        const previousYearStartDate = new Date(startDate)
        previousYearStartDate.setFullYear(previousYearStartDate.getFullYear() - 1)
        const previousYearEndDate = new Date(endDate)
        previousYearEndDate.setFullYear(previousYearEndDate.getFullYear() - 1)
        mopUserData && fetchComparedProductsInfo(previousYearStartDate, previousYearEndDate, abortController.signal)
      } else if (selectedComparison.type === 'custom') {
        const timeSpan = endDate.getTime() - startDate.getTime();
        const comparedEndDate = new Date(comparedStartDate.getTime() + timeSpan);
        setComparedDateRange([comparedStartDate, comparedEndDate])
      }
      mopUserData && selectedMarketplace && fetchMainProductsInfo(startDate, endDate, abortController.signal)
			return () => {
				abortController.abort()
			}
    }
  }, [mopUserData, selectedMarketplace, dateRange])

  useEffect(() => {
		const abortController = new AbortController()
    fetchOptimizationSets(abortController.signal)
    setSelectedPortfolioItems([])
    setSelectedProduct(null)
		setSelectedAdvancedOptions([])
		return () => {
      abortController.abort()
    }
  }, [selectedMarketplace])

  // fetch comparedProducts info onComparisonSelectChange
  useEffect(() => {
		const abortController = new AbortController()
    if (selectedComparison.type === 'none') {
      setSelectedLegend([legendOptions[0], legendOptions[2], legendOptions[4], legendOptions[6]])
    } else if (selectedComparison.type === 'month') {
      const previousMonthStartDate = new Date(startDate)
      previousMonthStartDate.setMonth(previousMonthStartDate.getMonth() - 1)
      const previousMonthEndDate = new Date(endDate)
      previousMonthEndDate.setMonth(previousMonthEndDate.getMonth() - 1)
      mopUserData && fetchComparedProductsInfo(previousMonthStartDate, previousMonthEndDate, abortController.signal)
      // add legends
      setSelectedLegend([legendOptions[0], legendOptions[1]])
    } else if (selectedComparison.type === 'year') {
      const previousYearStartDate = new Date(startDate)
      previousYearStartDate.setFullYear(previousYearStartDate.getFullYear() - 1)
      const previousYearEndDate = new Date(endDate)
      previousYearEndDate.setFullYear(previousYearEndDate.getFullYear() - 1)
      mopUserData && fetchComparedProductsInfo(previousYearStartDate, previousYearEndDate, abortController.signal)
      // add legends
      setSelectedLegend([legendOptions[0], legendOptions[1]])
    } else if (selectedComparison.type === 'custom') {
      const timeSpan = endDate.getTime() - startDate.getTime();
      const comparedEndDate = new Date(comparedStartDate.getTime() + timeSpan);
      setComparedDateRange([comparedStartDate, comparedEndDate])
      // add legends
      setSelectedLegend([legendOptions[0], legendOptions[1]])
    }
		return () => {
			abortController.abort()
		}
  }, [selectedComparison])
  useEffect(() => {
		const abortController = new AbortController()
    mopUserData && selectedComparison.type === 'custom' && fetchComparedProductsInfo(comparedStartDate, comparedEndDate, abortController.signal)
		return () => {
			abortController.abort()
		}
  }, [comparedDateRange])

  return (
    <div className="min-w-[800px] py-4 sm:py-6 px-8 sm:px-12">
			<div className="flex items-center justify-between">
				<h1 className="flex items-center gap-x-2 text-2xl text-gray-800 font-medium">
					<div className="border border-blue-600 bg-blue-100/40 text-blue-800 text-sm font-semibold px-2 py-1 rounded-md">
						Vendor
					</div>
					{t("title")}
				</h1>
				<CSVLink
					data={
						reportByDate.map((report: any) => {
							return {
								// CSV Columns
								"Date": formatUTCDate(report.date),
								"Returns (Sourcing)": report.sourcing_customer_returns,
								"Shipped COGs (Sourcing)": report.sourcing_shipped_cogs_amount,
								"Shipped Revenue (Sourcing)": report.sourcing_shipped_revenue_amount,
								"Shipped Units (Sourcing)": report.sourcing_shipped_units,
								"Returns (Manufacturing)": report.manufacturing_customer_returns,
								"Ordered Revenue (Manufacturing)": report.manufacturing_ordered_revenue_amount,
								"Ordered Units (Manufacturing)": report.manufacturing_ordered_units,
								"Shipped Revenue (Manufacturing)": report.manufacturing_shipped_revenue_amount,
								"Shipped COGs (Manufacturing)": report.manufacturing_shipped_cogs_amount,
								"Shipped Units (Manufacturing)": report.manufacturing_shipped_units,
								"Glance Views": report.glance_views,
								"Total Ad Spend": report.ad_cost + report.sd_cost,
								"Total Ad Sales": report.ad_sales + report.sd_sales,
								"Total Ad Sales Same SKU": report.ad_sales_same_sku + report.sd_sales_promoted_clicks,
								"Total Impressions": report.impressions + report.sd_impressions,
								"Total Clicks": report.clicks + report.sd_clicks,
								"Total ROAS": report.ad_cost + report.sd_cost === 0
									? 0
									: ((report.ad_sales + report.sd_sales) / (report.ad_cost + report.sd_cost) * 100).toFixed(2),
								"SP AD Spend": report.ad_cost,
								"SP AD Sales": report.ad_sales,
								"SP AD Sales Same SKU": report.ad_sales_same_sku,
								"SP AD Impressions": report.impressions,
								"SP AD Clicks": report.clicks,
								"SP ROAS": report.roas,
								"SD AD Spend": report.sd_cost,
								"SD AD Sales": report.sd_sales,
								"SD AD Promoted Click Sales": report.sd_sales_promoted_clicks,
								"SD AD Impressions": report.sd_impressions,
								"SD AD Clicks": report.sd_clicks,
								"SD ROAS": report.sd_roas
							}
						})
					}
					filename={selectedProfile.account_name + "_" + selectedMarketplace.country_code + "_data_" + formatDate(new Date()) + ".csv"}
					target="_blank">
					<div
						className={cn(
							"flex items-center gap-x-2 p-3 cursor-pointer rounded-md focus:outline-none text-sm overflow-hidden bg-blue-100 hover:bg-blue-200 text-blue-500 font-semibold",
							(dailyFetchCounter.current > 0 || reportByDate.length <= 0) && "cursor-not-allowed"
							)}
					>
						{dailyFetchCounter.current > 0
							? <svg className="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
									<circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
									<path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
								</svg>
							: <ArrowDownTrayIcon
									className="flex-shrink-0 h-4 w-4"
									aria-hidden="true"
								/>
						}
						<div>Export</div>
					</div>
				</CSVLink>
			</div>
			{/* filters */}
			<div className="mt-3 flex flex-wrap items-center gap-3">
				<div>
					<div className="text-xs text-gray-400 font-semibold">{t("filter.dateRange.label")} *</div>
					<DatePicker
						selectsRange={true}
						minDate={new Date('2023-01-01')}
						maxDate={new Date()}
						startDate={startDate}
						endDate={endDate}
						onChange={(update) => {
							setDateRange(update as Date[])
						}}
						dateFormat="yyyy.MM.dd"
						calendarClassName="dashboard-date-range"
						// @ts-ignore
						customInput={<DateRangeInput />}
						locale={locale}
					/>
				</div>
				<div>
					<div className="text-xs text-gray-400 font-semibold">{t("filter.attributionWindow.label")}</div>
					<AttributionWindowSelect
						className="mt-1 shadow-md rounded-lg"
						selected={selectedAttributionWindow}
						setSelected={setSelectedAttributionWindow}
					/>
				</div>
				{ portfolioListItems.length > 0 &&
				<div>
					<div className="flex items-center gap-x-2 text-xs text-gray-400 font-semibold">
						{t("filter.optimizationSet.label")}
						{ selectedPortfolioItems.length > 0 &&
						<div className="flex items-center justify-center text-xs font-semibold text-blue-400">
							{selectedPortfolioItems.length}
						</div>
						}
					</div>
					<OptimizationSetSelect
						className="mt-1 shadow-md rounded-lg"
						selected={selectedPortfolioItems}
						setSelected={setSelectedPortfolioItems}
						options={portfolioListItems}
						multiple={true}
					/>
				</div>
				}
				{selectedProduct &&
				<div>
					<div className="text-xs text-gray-400 font-semibold">{t("filter.productFilter.label")}</div>
					<div className="mt-1 relative w-full max-w-[400px] flex items-center gap-x-3 px-3 py-2 cursor-pointer bg-white text-left text-gray-500 text-xs font-normal rounded-md shadow-md">
						<div className="grow relative flex items-center gap-x-4 overflow-hidden">
							{selectedProduct.image
							? (<img src={selectedProduct.image} alt="Item Image" className="flex-shrink-0 w-5 h-5 rounded-full" />)
							: (<div className="flex-shrink-0 flex items-center justify-center w-5 h-5 bg-gray-100 rounded-full">
									<ExclamationTriangleIcon className="h-2 w-2 text-gray-300" />
								</div>)
							}
							<div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
								<div className="flex items-center gap-x-2">
									<div className="flex-shrink-0 text-xs text-red-400 font-semibold">{formatCurrency(selectedProduct.lowest_price)}</div>
									<div className="text-xs text-gray-500 text-left font-semibold truncate">
										{selectedProduct.item_name
											? selectedProduct.item_name
											: "No Title"
										}
									</div>
								</div>
							</div>
						</div>
						<button onClick={() => setSelectedProduct(null)}>
							<XMarkIcon className="h-4 w-4 text-gray-400 hover:text-gray-500" />
						</button>
					</div>
				</div>
				}
				<div>
					<div className="text-xs text-gray-400 font-semibold">{t("filter.compareWith.label")}</div>
					<ComparisonSelect
						className="mt-1 shadow-md rounded-lg"
						selected={selectedComparison}
						setSelected={setSelectedComparison}
					/>
				</div>
				{selectedComparison.type === 'custom' &&
				<div>
					<div className="text-xs text-gray-400 font-semibold">{t("filter.customPeriod.label")}</div>
					<DatePicker
						selectsRange={true}
						startDate={comparedStartDate}
						endDate={comparedEndDate}
						onChange={(update) => {
							const newStartDate = update[0] as Date
							const timeSpan = endDate.getTime() - startDate.getTime();
							const newEndDate = new Date(newStartDate.getTime() + timeSpan);
							setComparedDateRange([newStartDate, newEndDate])
						}}
						dateFormat="yyyy.MM.dd"
						calendarClassName="dashboard-date-range"
						// @ts-ignore
						customInput={<DateRangeInput />}
						locale={locale}
					/>
				</div>
				}
				<div>
					<div className="text-xs text-gray-400 font-semibold">{t("filter.advancedFilter.label")}</div>
					<AdvancedFilterSelect
						className="mt-1 shadow-md rounded-lg"
						advancedFilterOptions={advancedFilterOptions}
						selected={selectedAdvancedOptions}
						setSelected={setSelectedAdvancedOptions}
					/>
				</div>
			</div>
			{/* graph */}
			<div className="mt-4 relative lg:h-[380px] flex flex-col lg:flex-row items-center gap-x-3">
				<div className="flex-shrink-0 grid grid-cols-2 lg:grid-cols-1 gap-3 w-full lg:w-fit lg:h-full">
					<div className="flex flex-col justify-center gap-y-3 py-3 px-3 bg-white shadow-md rounded-lg">
						{/* to be developed */}
						<div className="relative w-full flex flex-col items-start gap-x-3 pb-1 px-3">
							<div className="flex-shrink-0 flex items-center gap-x-1 text-lg text-purple-900/60 font-semibold text-center">
								<CurrencyDollarIcon className="w-5 h-5"/>
								{t("keyMetrics.totalMetrics.estMargin")}
								<Popover className="relative flex items-center justify-center">
									{({ open }) => (
										<>
											<Popover.Button
												className={cn(
													"inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none",
												)}
											>
												<QuestionMarkCircleIcon className="w-5 h-5"/>
											</Popover.Button>
											<Transition
												as={Fragment}
												enter="transition ease-out duration-200"
												enterFrom="opacity-0 translate-y-1"
												enterTo="opacity-100 translate-y-0"
												leave="transition ease-in duration-150"
												leaveFrom="opacity-100 translate-y-0"
												leaveTo="opacity-0 translate-y-1"
											>
												<Popover.Panel className="absolute top-full left-1/2 z-10 mt-2 -translate-x-1/2 transform">
													<div className="w-[200px] p-2 rounded-lg shadow-lg bg-gray-900/90 text-xs text-white font-normal">
														{t("keyMetrics.metricsTooltip.vendorEstimatedMargin")}
													</div>
												</Popover.Panel>
											</Transition>
										</>
									)}
								</Popover>
							</div>
							{vendorFetchCounter.current > 0
								? <div className="animate-pulse pt-1">
										<div className="w-[120px] h-8 rounded-md bg-gray-100"/>
									</div>
								: <div className="mt-1 w-full flex items-center justify-between text-2xl text-gray-600 font-semibold text-center">
										{totalCogsRevenue === 0 ? '-' : (((totalCogsRevenue - totalSalesReturned - totalFee - adCost) / totalCogsRevenue) * 100).toLocaleString(undefined, {maximumFractionDigits: 2})}%
										{selectedComparison.type !== 'none' &&
											<div
												className={cn(
													"mt-1 flex items-center justify-center gap-x-1",
													totalCogsRevenue === 0 || comparedTotalCogsRevenue === 0
														? "text-gray-400"
														: ((totalCogsRevenue - totalSalesReturned - totalFee - adCost) / totalCogsRevenue) > ((comparedTotalCogsRevenue - comparedTotalSalesReturned - comparedTotalFee - comparedAdCost) / comparedTotalCogsRevenue)
															? "text-red-400"
															: "text-blue-400"
												)}
											>
												<ArrowDownCircleIcon
													className={cn(
														"h-4 w-4 inline-block",
														((totalCogsRevenue - totalSalesReturned - totalFee - adCost) / totalCogsRevenue) < ((comparedTotalCogsRevenue - comparedTotalSalesReturned - comparedTotalFee - comparedAdCost) / comparedTotalCogsRevenue)
															? ""
															: "transform rotate-180"
													)}
												/>
												<span className="text-xs">
													{totalCogsRevenue === 0 || comparedTotalCogsRevenue === 0
														? '0%'
														: `${((((totalCogsRevenue - totalSalesReturned - totalFee - adCost) / totalCogsRevenue) - ((comparedTotalCogsRevenue - comparedTotalSalesReturned - comparedTotalFee - comparedAdCost) / comparedTotalCogsRevenue)) / ((comparedTotalCogsRevenue - comparedTotalSalesReturned - comparedTotalFee - comparedAdCost) / comparedTotalCogsRevenue) * 100).toLocaleString(undefined, {maximumFractionDigits: 2})}%`
													}
												</span>
											</div>
										}
									</div>
							}
						</div>
						<div className="relative w-full grid grid-cols-2 gap-x-3">
							<div className="flex-shrink-0 w-full relative flex flex-col items-start justify-start px-3 rounded-lg bg-white/50">
								<div className="flex-shrink-0 flex items-center justify-center text-sm text-gray-400 font-semibold text-center">
									{t('keyMetrics.totalMetrics.totalCogsRevenue')} 
									<Popover className="relative flex items-center justify-center pl-1">
										{({ open }) => (
											<>
												<Popover.Button
													className={cn(
														"inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none",
													)}
												>
													<QuestionMarkCircleIcon className="w-5 h-5"/>
												</Popover.Button>
												<Transition
													as={Fragment}
													enter="transition ease-out duration-200"
													enterFrom="opacity-0 translate-y-1"
													enterTo="opacity-100 translate-y-0"
													leave="transition ease-in duration-150"
													leaveFrom="opacity-100 translate-y-0"
													leaveTo="opacity-0 translate-y-1"
												>
													<Popover.Panel className="absolute top-full left-1/2 z-10 mt-2 -translate-x-1/2 transform">
														{t.rich("keyMetrics.metricsTooltip.totalCogsRevenue",{
															value: formatCurrency(totalRevenue),
															first: (chunks) => <div className="w-[200px] p-2 rounded-lg shadow-lg bg-gray-900/90 text-xs text-white font-normal">{chunks}</div>
														})}
													</Popover.Panel>
												</Transition>
											</>
										)}
									</Popover>
								</div>
								{vendorFetchCounter.current > 0
									? <div className="animate-pulse pt-1">
											<div className="w-[120px] h-6 rounded-md bg-gray-100"/>
										</div>
									: <div className="text-lg text-gray-600 font-bold text-center">
											{formatCurrency(totalCogsRevenue)}
											{selectedComparison.type !== 'none' &&
												<div
													className={cn(
														"mt-1 flex items-center justify-center gap-x-1",
														totalCogsRevenue === comparedTotalCogsRevenue
															? "text-gray-400"
															: totalCogsRevenue > comparedTotalCogsRevenue
																? "text-red-400"
																: "text-blue-400"
													)}
												>
													<ArrowDownCircleIcon
														className={cn(
															"h-4 w-4 inline-block",
															totalCogsRevenue < comparedTotalCogsRevenue
																? ""
																: "transform rotate-180"
														)}
													/>
													<span className="text-xs">
														{((totalCogsRevenue - comparedTotalCogsRevenue) / comparedTotalCogsRevenue * 100).toLocaleString(undefined, {maximumFractionDigits: 2})}%
													</span>
												</div>
											}
										</div>
								}
							</div>
							<div className="flex-shrink-0 w-full relative flex flex-col items-start justify-start px-3 rounded-lg bg-white/50">
								<div className="flex-shrink-0 flex items-center justify-center text-sm text-gray-400 font-semibold text-center">
									{t("keyMetrics.totalMetrics.estFee")}
									<Popover className="relative flex items-center justify-center pl-1">
										{({ open }) => (
											<>
												<Popover.Button
													className={cn(
														"inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none",
													)}
												>
													<QuestionMarkCircleIcon className="w-5 h-5"/>
												</Popover.Button>
												<Transition
													as={Fragment}
													enter="transition ease-out duration-200"
													enterFrom="opacity-0 translate-y-1"
													enterTo="opacity-100 translate-y-0"
													leave="transition ease-in duration-150"
													leaveFrom="opacity-100 translate-y-0"
													leaveTo="opacity-0 translate-y-1"
												>
													<Popover.Panel className="absolute top-full left-1/2 z-10 mt-2 -translate-x-1/2 transform">
														<div className="w-[200px] p-2 rounded-lg shadow-lg bg-gray-900/90 text-xs text-white font-normal">
															{t("keyMetrics.metricsTooltip.estFee")}
														</div>
													</Popover.Panel>
												</Transition>
											</>
										)}
									</Popover>
								</div>
								{vendorFetchCounter.current > 0
									? <div className="animate-pulse pt-1">
											<div className="w-[120px] h-6 rounded-md bg-gray-100"/>
										</div>
									: <div className="text-lg text-gray-600 font-bold text-center">
									{formatCurrency(totalFee)}
									{selectedComparison.type !== 'none' &&
										<div
											className={cn(
												"mt-1 flex items-center justify-center gap-x-1",
												totalFee === comparedTotalFee
													? "text-gray-400"
													: totalFee > comparedTotalFee
														? "text-red-400"
														: "text-blue-400"
											)}
										>
											<ArrowDownCircleIcon
												className={cn(
													"h-4 w-4 inline-block",
													totalFee < comparedTotalFee
														? ""
														: "transform rotate-180"
												)}
											/>
											<span className="text-xs">
												{((totalFee - comparedTotalFee) / comparedTotalFee * 100).toLocaleString(undefined, {maximumFractionDigits: 2})}%
											</span>
										</div>
									}
										</div>
								}
							</div>
						</div>
					</div>
					<div className="flex flex-col justify-center gap-y-3 py-3 px-3 bg-white shadow-md rounded-lg">
						{/* to be developed */}
						<div className="relative w-full flex flex-col items-start gap-x-3 pb-1 px-3">
							<div className="flex-shrink-0 flex items-center gap-x-1 text-lg text-blue-900/60 font-semibold text-center">
								<PresentationChartLineIcon className="w-5 h-5"/>
								{t("keyMetrics.adMetrics.roas")}
								<Popover className="relative flex items-center justify-center">
									{({ open }) => (
										<>
											<Popover.Button
												className={cn(
													"inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none",
												)}
											>
												<QuestionMarkCircleIcon className="w-5 h-5"/>
											</Popover.Button>
											<Transition
												as={Fragment}
												enter="transition ease-out duration-200"
												enterFrom="opacity-0 translate-y-1"
												enterTo="opacity-100 translate-y-0"
												leave="transition ease-in duration-150"
												leaveFrom="opacity-100 translate-y-0"
												leaveTo="opacity-0 translate-y-1"
											>
												<Popover.Panel className="absolute top-full left-1/2 z-10 mt-2 -translate-x-1/2 transform">
													<div className="w-[200px] p-2 rounded-lg shadow-lg bg-gray-900/90 text-xs text-white font-normal">
														{t("keyMetrics.metricsTooltip.roas")}
													</div>
												</Popover.Panel>
											</Transition>
										</>
									)}
								</Popover>
							</div>
							{vendorFetchCounter.current > 0
								? <div className="animate-pulse pt-1">
										<div className="w-[120px] h-8 rounded-md bg-gray-100"/>
									</div>
								: <div className="mt-1 w-full flex items-center justify-between text-2xl text-gray-600 font-semibold text-center">
										{adCost === 0 ? '-' : ((adSales / adCost) * 100).toLocaleString(undefined, {maximumFractionDigits: 2})}%
										{selectedComparison.type !== 'none' &&
											<div
												className={cn(
													"mt-1 flex items-center justify-center gap-x-1",
													adCost === 0 || comparedAdCost === 0
														? "text-gray-400"
														: (adSales / adCost) > (comparedAdSales / comparedAdCost)
															? "text-red-400"
															: "text-blue-400"
												)}
											>
												<ArrowDownCircleIcon
													className={cn(
														"h-4 w-4 inline-block",
														(adSales / adCost) < (comparedAdSales / comparedAdCost)
															? ""
															: "transform rotate-180"
													)}
												/>
												<span className="text-xs">
													{adCost === 0 || comparedAdCost === 0
														? '0%'
														: `${(((adSales / adCost) - (comparedAdSales / comparedAdCost)) / (comparedAdSales / comparedAdCost) * 100).toLocaleString(undefined, {maximumFractionDigits: 2})}%`
													}
												</span>
											</div>
										}
									</div>
							}
						</div>
						<div className="relative w-full grid grid-cols-2 gap-x-3">
							<div className="flex-shrink-0 w-full relative flex flex-col items-start justify-start px-3 rounded-lg bg-white/50">
								<div className="text-sm text-gray-400 font-semibold text-center">
									{t("keyMetrics.adMetrics.adSales")}
								</div>
								{vendorFetchCounter.current > 0
									? <div className="animate-pulse pt-1">
											<div className="w-[120px] h-6 rounded-md bg-gray-100"/>
										</div>
									: <div className="text-lg text-gray-600 font-bold text-center">
									{formatCurrency(adSales)}
									{selectedComparison.type !== 'none' &&
										<div
											className={cn(
												"mt-1 flex items-center justify-center gap-x-1",
												adSales === comparedAdSales
													? "text-gray-400"
													: adSales > comparedAdSales
														? "text-red-400"
														: "text-blue-400"
											)}
										>
											<ArrowDownCircleIcon
												className={cn(
													"h-4 w-4 inline-block",
													adSales < comparedAdSales
														? ""
														: "transform rotate-180"
												)}
											/>
											<span className="text-xs">
												{((adSales - comparedAdSales) / comparedAdSales * 100).toLocaleString(undefined, {maximumFractionDigits: 2})}%
											</span>
										</div>
									}
										</div>
								}
							</div>
							<div className="flex-shrink-0 w-full relative flex flex-col items-start justify-start px-3 rounded-lg bg-white/50">
								<div className="flex-shrink-0 flex items-center justify-center text-sm text-gray-400 font-semibold text-center">
									{t("keyMetrics.adMetrics.adSpend")}
								</div>
								{vendorFetchCounter.current > 0
									? <div className="animate-pulse pt-1">
											<div className="w-[120px] h-6 rounded-md bg-gray-100"/>
										</div>
									: <div className="text-lg text-gray-600 font-bold text-center">
											{formatCurrency(adCost)}
											{selectedComparison.type !== 'none' &&
												<div
													className={cn(
														"mt-1 flex items-center justify-center gap-x-1",
														adCost === comparedAdCost
															? "text-gray-400"
															: adCost > comparedAdCost
																? "text-red-400"
																: "text-blue-400"
													)}
												>
													<ArrowDownCircleIcon
														className={cn(
															"h-4 w-4 inline-block",
															adCost < comparedAdCost
																? ""
																: "transform rotate-180"
														)}
													/>
													<span className="text-xs">
														{((adCost - comparedAdCost) / comparedAdCost * 100).toLocaleString(undefined, {maximumFractionDigits: 2})}%
													</span>
												</div>
											}
										</div>
								}
							</div>
						</div>
					</div>
				</div>
				<div className="mt-3 lg:mt-0 grow flex flex-col w-full lg:w-auto min-w-0 h-[380px] lg:h-full p-3 rounded-lg bg-white shadow-md">
					<TabGroup
						className="relative flex flex-col h-full"
						selectedIndex={selectedGraphTabIndex}
						onChange={setSelectedGraphTabIndex}
					>
						<TabPanels className="grow relative flex flex-col min-h-0">
							<TabPanel className="flex flex-col w-full h-full">
								{vendorFetchCounter.current > 0
                  					? <div className="animate-pulse relative flex flex-col w-full h-full">
										<div className="w-[260px] h-[38px] rounded-md bg-gray-100">
										</div>
										<div className="mt-3 grow w-full rounded-md bg-gray-100">
										</div>
									</div>
                 					 : <>
										<LegendSelect
											className="mb-1 pr-[192px]"
											compared={selectedComparison.type !== 'none'}
											legendOptions={legendOptions}
											selected={selectedLegend}
											setSelected={setSelectedLegend}
										/>
										<VendorPlotGraph 
											selectedLegend={selectedLegend} 
											data={reportByDate} 
											comparedData={comparedReportByDate}
											currencyCode={selectedMarketplace?.default_currency_code || 'USD'}
										/>
									</>
								}
							</TabPanel>
							<TabPanel className="flex flex-col w-full h-full">
								{hourlyfetchCounter.current > 0
									? <div className="animate-pulse relative flex flex-col w-full h-full">
										<div className="w-[260px] h-[38px] rounded-md bg-gray-100">
										</div>
										<div className="mt-3 grow w-full rounded-md bg-gray-100">
										</div>
									</div>
									: <>
										<LegendSelect
											className="mb-1 pr-[192px]"
											compared={false}
											legendOptions={hourlyLegendOptions}
                      						selected={selectedHourlyLegend}
                      						setSelected={setSelectedHourlyLegend}
										/>
										<VendorHourlyPlotGraph 
											selectedLegend={selectedHourlyLegend} 
											data={reportByHour}
											currencyCode={selectedMarketplace?.default_currency_code || 'USD'}
										/>
									</>
								}
							</TabPanel>
						</TabPanels>
						<div className="hidden absolute top-0 right-0">
							<TabList className="inline-flex gap-x-1 bg-gray-100 p-1 rounded-lg">
								<Tab
									className="flex items-center gap-x-1 py-[5px] px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
								>
									{t("graph.tab.daily")}
								</Tab>
								<Tab
									className="flex items-center gap-x-1 py-[5px] px-4 text-sm font-semibold text-gray-400 focus:outline-none data-[selected]:text-gray-500 data-[selected]:bg-white data-[selected]:shadow-md data-[selected]:data-[hover]:bg-white data-[hover]:bg-white/40 data-[focus]:outline-1 data-[focus]:outline-white rounded-lg"
								>
									{t("graph.tab.hourly")}
									<Popover className="relative flex items-center justify-center">
										{({ open }) => (
											<>
												<PopoverButton
													className={cn(
														"inline-flex items-center justify-center rounded-full text-gray-300 hover:text-gray-400 focus:outline-none"
													)}
												>
													<QuestionMarkCircleIcon className="w-5 h-5"/>
												</PopoverButton>
												<Transition
													as={Fragment}
													enter="transition ease-out duration-200"
													enterFrom="opacity-0 translate-y-1"
													enterTo="opacity-100 translate-y-0"
													leave="transition ease-in duration-150"
													leaveFrom="opacity-100 translate-y-0"
													leaveTo="opacity-0 translate-y-1"
												>
													<PopoverPanel className="absolute right-full top-0 z-10 mr-2 w-screen max-w-xs translate-y-0 transform px-4 sm:px-0">
														<div className="overflow-hidden rounded-lg shadow-lg">
															<div className="relative p-4 bg-gray-900/90 text-xs text-white font-normal text-left">
																{t.rich("graph.tab.tabTooltip.hourly",{
																	enter: () =>  <br/>
																})}
															</div>
														</div>
													</PopoverPanel>
												</Transition>
											</>
										)}
									</Popover>
								</Tab>
							</TabList>
						</div>
					</TabGroup>
				</div>
			</div>
			{/* table */}
			<div className="w-full mt-3">
				<div
					className={cn(
					'rounded-lg bg-white shadow-md overflow-hidden',
					)}
				>
					{vendorFetchCounter.current > 0
						? <div className="animate-pulse w-full p-6">
								<div className="flex-1 space-y-6 py-1">
									<div className="h-[38px] bg-gray-100 rounded-md"></div>
									<div className="space-y-3">
										<div className="grid grid-cols-3 gap-4">
											<div className="h-[38px] bg-gray-100 rounded-md col-span-1"></div>
											<div className="h-[38px] bg-gray-100 rounded-md col-span-2"></div>
											<div className="h-[38px] bg-gray-100 rounded-md col-span-1"></div>
											<div className="h-[38px] bg-gray-100 rounded-md col-span-2"></div>
											<div className="h-[38px] bg-gray-100 rounded-md col-span-1"></div>
											<div className="h-[38px] bg-gray-100 rounded-md col-span-2"></div>
											<div className="h-[38px] bg-gray-100 rounded-md col-span-1"></div>
											<div className="h-[38px] bg-gray-100 rounded-md col-span-2"></div>
										</div>
									</div>
								</div>
							</div>
						: <div className="">
								<VendorPinnedColTable
									data={reportByAsin}
									selectedAttributionWindow={selectedAttributionWindow}
									selectedProduct={selectedProduct}
									setSelectedProduct={setSelectedProduct}
									selectedMarketplace={selectedMarketplace}/>
							</div>
					}
				</div>
			</div>
    </div>
  )
}
