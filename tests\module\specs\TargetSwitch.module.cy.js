/// <reference types="cypress" />

import BasePage from '../../pages/BasePage';
import {
  setupAddTargetModalMocks,
  setupAddTargetModalErrorMocks,
  selectors,
} from '../mock/AdPortfolio';

class TargetSwitchPage extends BasePage {
  constructor() {
    super();
    Object.assign(this, selectors);
  }

  // Switch component methods
  assertSwitchExists() {
    cy.get(this.switch.container).should('be.visible');
  }

  assertSwitchIsEnabled(index = 0) {
    this.getSwitchInTargetItem(index).should('be.checked');
  }

  assertSwitchIsDisabled(index = 0) {
    this.getSwitchInTargetItem(index).should('not.be.checked');
  }

  clickSwitch(index = 0) {
    this.getSwitchInTargetItem(index).click({ force: true });
  }

  assertSwitchIsLoading(index = 0) {
    // Check if switch is disabled during loading
    this.getSwitchInTargetItem(index).should('be.disabled');
  }

  assertSwitchIsNotLoading(index = 0) {
    this.getSwitchInTargetItem(index).should('not.be.disabled');
  }

  // Helper methods for target list
  getTargetListItem(index = 0) {
    return cy.get(this.switch.targetListItem).eq(index);
  }

  getSwitchInTargetItem(index = 0) {
    return this.getTargetListItem(index).find(this.switch.input);
  }

  // Navigation helpers
  navigateToTargetList() {
    // Navigate to dashboard
    cy.visit('/dashboard');
    // Click on a product to open target list
    cy.get(this.targets.productItem).first().click();
    // Click on an ad group to show targets
    cy.get(this.targets.adGroupItem).first().click();
  }

  // Assertion helpers
  assertTargetListVisible() {
    cy.get(this.switch.targetListItem).should('have.length.greaterThan', 0);
  }

  assertTargetState(index, expectedState) {
    this.getTargetListItem(index).within(() => {
      if (expectedState === 'ENABLED') {
        cy.get(this.switch.input).should('be.checked');
      } else {
        cy.get(this.switch.input).should('not.be.checked');
      }
    });
  }

  getTargetText(index = 0) {
    return this.getTargetListItem(index).find('.text-xs').first();
  }
}

describe('Target Switch Component E2E Tests', () => {
  const page = new TargetSwitchPage();

  beforeEach(() => {
    cy.login();
    setupAddTargetModalMocks();
  });

  describe('Switch Display and State', () => {
    beforeEach(() => {
      page.navigateToTargetList();
    });

    it('should display switch components for each target', () => {
      page.assertTargetListVisible();
      page.assertSwitchExists();
    });

    it('should show correct initial states for targets', () => {
      // Based on mock data, first target should be enabled
      page.assertSwitchIsEnabled(0);
      // Second target should be disabled/paused
      page.assertSwitchIsDisabled(1);
    });

    it('should display different target types with switches', () => {
      // Verify that both keyword and product targets have switches
      page.getTargetListItem(0).should('contain', 'KEYWORD');
      page.getTargetListItem(1).should('contain', 'PRODUCT');

      // Both should have switches
      page.getSwitchInTargetItem(0).should('exist');
      page.getSwitchInTargetItem(1).should('exist');
    });
  });

  describe('Switch Functionality', () => {
    beforeEach(() => {
      page.navigateToTargetList();
    });

    it('should toggle target status when switch is clicked', () => {
      // Get initial state of first switch
      page.getSwitchInTargetItem(0).then(($switch) => {
        const wasChecked = $switch.is(':checked');

        // Click the switch
        page.clickSwitch(0);

        // Verify API call was made with correct parameters
        cy.wait('@changeTargetStatus').then((interception) => {
          const expectedState = wasChecked ? 'PAUSED' : 'ENABLED';
          expect(interception.request.url).to.include(`state=${expectedState}`);
        });

        // Verify switch state changed
        if (wasChecked) {
          page.assertSwitchIsDisabled(0);
        } else {
          page.assertSwitchIsEnabled(0);
        }
      });
    });

    it('should handle multiple switch toggles correctly', () => {
      // Toggle first switch
      page.clickSwitch(0);
      cy.wait('@changeTargetStatus');

      // Toggle second switch
      page.clickSwitch(1);
      cy.wait('@changeTargetStatus');

      // Both API calls should have been made
      cy.get('@changeTargetStatus.all').should('have.length', 2);
    });

    it('should show loading state during switch toggle', () => {
      // Mock a slower API response
      cy.intercept(
        'PUT',
        '/api/optimization/change_status_of_target*',
        (req) => {
          req.reply((res) => {
            res.delay(1000);
            res.send({ status: true, message: 'Target status updated' });
          });
        }
      ).as('slowChangeTargetStatus');

      // Click switch
      page.clickSwitch(0);

      // Should be disabled during loading
      page.assertSwitchIsLoading(0);

      // Wait for API response
      cy.wait('@slowChangeTargetStatus');

      // Should be enabled again after loading
      page.assertSwitchIsNotLoading(0);
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      page.navigateToTargetList();
    });

    it('should handle API error gracefully', () => {
      // Mock API error
      setupAddTargetModalErrorMocks();

      // Get initial state
      page.getSwitchInTargetItem(0).then(($switch) => {
        const initialState = $switch.is(':checked');

        // Click switch
        page.clickSwitch(0);

        cy.wait('@changeTargetStatusError');

        // Switch should return to original state on error
        if (initialState) {
          page.assertSwitchIsEnabled(0);
        } else {
          page.assertSwitchIsDisabled(0);
        }
      });
    });

    it('should handle network timeout', () => {
      // Mock network timeout
      cy.intercept('PUT', '/api/optimization/change_status_of_target*', {
        forceNetworkError: true,
      }).as('networkError');

      page.clickSwitch(0);

      // Should handle network error gracefully
      cy.wait('@networkError');

      // Switch should not be stuck in loading state
      page.assertSwitchIsNotLoading(0);
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      page.navigateToTargetList();
    });

    it('should be keyboard accessible', () => {
      // Focus on first switch using keyboard
      page.getSwitchInTargetItem(0).focus();

      // Should be focusable
      page.getSwitchInTargetItem(0).should('be.focused');

      // Should be toggleable with space key
      page.getSwitchInTargetItem(0).type(' ');

      cy.wait('@changeTargetStatus');
    });

    it('should have proper ARIA attributes', () => {
      page.getSwitchInTargetItem(0).should('have.attr', 'type', 'checkbox');
      page.getSwitchInTargetItem(0).should('have.attr', 'role', 'switch');
    });
  });

  describe('Visual States', () => {
    beforeEach(() => {
      page.navigateToTargetList();
    });

    it('should show visual feedback for enabled state', () => {
      // Enabled switch should have specific styling
      page.getTargetListItem(0).within(() => {
        cy.get(page.switch.container).should('have.class', 'Mui-checked');
      });
    });

    it('should show visual feedback for disabled state', () => {
      // Disabled switch should not have checked class
      page.getTargetListItem(1).within(() => {
        cy.get(page.switch.container).should('not.have.class', 'Mui-checked');
      });
    });

    it('should show loading indicator during state change', () => {
      // Mock slow response
      cy.intercept(
        'PUT',
        '/api/optimization/change_status_of_target*',
        (req) => {
          req.reply((res) => {
            res.delay(2000);
            res.send({ status: true });
          });
        }
      ).as('slowResponse');

      page.clickSwitch(0);

      // Should show some loading indication (disabled state)
      page.assertSwitchIsLoading(0);

      cy.wait('@slowResponse');
    });
  });

  describe('Integration with Target List', () => {
    beforeEach(() => {
      page.navigateToTargetList();
    });

    it('should update target list after successful toggle', () => {
      // Click switch to change state
      page.clickSwitch(0);

      cy.wait('@changeTargetStatus');

      // Target list should refresh and show updated state
      // This depends on the implementation - might trigger a refresh
      cy.wait('@getTargets');
    });

    it('should work with filtered target lists', () => {
      // If there's a search/filter functionality
      // Type in search to filter targets
      cy.get('[data-testid="target-search"]').type('wireless');

      // Should still be able to toggle filtered targets
      page.assertSwitchExists();
      page.clickSwitch(0);

      cy.wait('@changeTargetStatus');
    });

    it('should work with different target types', () => {
      // Test switches work for both keyword and product targets
      const targetTypes = ['KEYWORD', 'PRODUCT'];

      targetTypes.forEach((type, index) => {
        page.getTargetListItem(index).should('contain', type);
        page.clickSwitch(index);
        cy.wait('@changeTargetStatus');
      });
    });
  });
});
