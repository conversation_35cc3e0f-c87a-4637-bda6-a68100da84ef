import type { PortfolioListItem, ProductListItem } from "@/components/dashboard/ad-portfolio-layout-component";

// Map brief optimizations response to PortfolioListItem[] expected by UI
export function adaptOptimizationsBriefToPortfolioList(brief: any[]): PortfolioListItem[] {
  if (!Array.isArray(brief)) return [] as unknown as PortfolioListItem[];
  const mapped = brief.map((it: any) => {
    const top = it?.top_product || null;

    // Minimal product object for header thumbnail/name display
    const topProduct: any = top
      ? {
          asin: top.asin,
          sku: top.sku,
          item_name: top.item_name,
          image: top.image,
          listing_price: top.listing_price ?? null,
          eligibility_status: (top.eligibility_status || "UNKNOWN").toUpperCase(),
          target_status: "",
          request_status: it?.request_status || "",
          // Required by UI in various spots
          marketplace_id: it?.marketplace_id,
        }
      : null;

    const portfolio: any = {
      id: it.optimization_id,
      account_id: it.account_id,
      marketplace_id: it.marketplace_id,
      portfolio_id: it.portfolio_id ?? "",
      creation_datetime: it.creation_datetime,
      ad_budget_amount: it.ad_budget_amount ?? null,
      ad_budget_end_date: it.ad_budget_end_date ?? null,
      ad_budget_start_date: it.ad_budget_start_date ?? null,
      ad_budget_type: it.ad_budget_type_code || it.ad_budget_type,
      use_yn: it.use_yn,
      bid_yn: it.bid_yn,
      display_yn: it.display_yn,
      optimization_goal: it.optimization_goal_code || it.optimization_goal,
      optimization_name: it.optimization_name,
      optimization_option: it.optimization_option,
      optimization_range: it.optimization_range,
      optimization_target_type: it.optimization_target_type,
      optimization_target_value: it.optimization_target_value,
      request_status: it.request_status,
      // UI reads first product image/name in header. We seed with top product only.
      target_products: topProduct ? [topProduct] : [],
      top_product_snapshot: topProduct || undefined,
      // prediction from brief (budget_usage.*)
      prediction: it.prediction || undefined,
      // Keep extra props used by edit forms
      competition_option: it.competition_option,
      target_same_sku_only_yn: it.target_same_sku_only_yn,
      limit_cpc: it.limit_cpc ?? 0,
      // Convenience flags for badges
      has_ineligible_asin: !!it.has_ineligible_asin,
      ineligible_asin_count: it.ineligible_asin_count ?? 0,
    };

    return portfolio as PortfolioListItem;
  });

  return mapped as unknown as PortfolioListItem[];
}

// Map optimization asins response to ProductListItem[] expected by UI
export function adaptOptimizationAsinsToProductList(res: any): ProductListItem[] {
  const asins = Array.isArray(res?.asins) ? res.asins : [];
  const mapped = asins.map((p: any) => {
    const prod: any = {
      asin: p.asin,
      sku: p.sku,
      item_name: p.item_name,
      image: p.image,
      listing_price: p.listing_price ?? null,
      eligibility_status: (p.eligibility_status || "UNKNOWN").toUpperCase(),
      target_status: p.target_status || "",
      request_status: p.request_status || "",
      // inventory info (used for warning UI in sliders)
      inventory: p.inventory || null,
    };
    return prod as ProductListItem;
  });
  return mapped as unknown as ProductListItem[];
}

// Merge asin detail into a single ProductListItem-like shape (new campaigns shape only)
export function adaptOptimizationAsinDetail(res: any): ProductListItem {
  const p = res || {};
  const meta = p.product || {};

  const campaigns = (Array.isArray(p.campaigns) ? p.campaigns : []).map((c: any) => {
    const adGroups = Array.isArray(c.ad_groups) ? c.ad_groups : []
    return {
      campaign_id: c.campaign_id,
      campaign_name: c.campaign_name,
      campaign_state: c.campaign_state,
      campaign_mop_yn: c.campaign_mop_yn ?? "Y",
      campaign_targeting_settings: (c.campaign_targeting_settings || "").toString().toUpperCase(),
      campaign_start_date: c.campaign_start_date,
      campaign_budget: c.campaign_budget,
      campaign_budget_usage_percent: c.campaign_budget_usage_percent,
      last_updated_date_time: c.last_updated_date_time,
      ad_groups: adGroups.map((g: any) => ({
        ad_group_id: g.ad_group_id,
        ad_group_name: g.ad_group_name,
        ad_group_state: g.ad_group_state,
        ad_group_type: g.ad_group_type,
        ad_group_mop_yn: g.ad_group_mop_yn,
        ad_group_creation_date_time: g.ad_group_creation_date_time,
        ads: Array.isArray(g.ads) ? g.ads : [],
      }))
    }
  })

  const prod: any = {
    asin: p.asin || meta.asin,
    sku: p.sku || meta.sku,
    item_name: meta.item_name,
    image: meta.image,
    listing_price: meta.listing_price ?? null,
    eligibility_status: (meta.eligibility_status || "UNKNOWN").toUpperCase(),
    target_status: p.target_status || "",
    request_status: p.request_status || "",
    inventory: p.inventory || null,
    campaigns,
  };
  return prod as unknown as ProductListItem;
} 