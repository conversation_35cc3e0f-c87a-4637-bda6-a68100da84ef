/// <reference types="cypress" />

import AddTargetModalPage from '../../pages/AddTargetModalPage';
import {
  setupOptimizationSetsSwitchMocks,
  setupOptimizationSetsSwitchErrorMocks,
  validationTestData,
} from '../mock/OptimizationSetsSwitchMock';

describe('AddTargetModal từ OptimizationSets E2E Tests', () => {
  const page = new AddTargetModalPage();

  beforeEach(() => {
    cy.login();
    setupOptimizationSetsSwitchMocks();
  });

  describe('Luồng Navigation từ OptimizationSets', () => {
    it('Nên điều hướng từ OptimizationSets → Product → AdGroup → AddTargetModal', () => {
      // Bước 1: Điều hướng đến OptimizationSets
      page.navigateToOptimizationSets();
      cy.wait('@getListOptimizations');
      
      // Bước 2: Click vào optimization set đầu tiên
      cy.get(page.navigation.optimizationSetItem).first().find('.py-8').click();
      
      // Bước 3: Click vào product đầu tiên
      cy.get(page.navigation.productItem).first().click();
      
      // Bước 4: Click vào ad group đầu tiên
      cy.get(page.navigation.adGroupItem).first().click();
      
      // Bước 5: Kiểm tra Add Target button xuất hiện
      cy.get(page.navigation.addTargetButton).should('be.visible');
      
      // Bước 6: Mở AddTargetModal
      page.openModal();
      page.assertModalTitle('Add Target');
    });

    it('Nên hiển thị Switch components trong cùng view với Add Target button', () => {
      page.navigateToAdGroupTargets();
      
      // Kiểm tra cả Switch và Add Target button đều hiển thị
      page.assertSwitchExists();
      cy.get(page.navigation.addTargetButton).should('be.visible');
    });
  });

  describe('AddTargetModal Form Tests', () => {
    beforeEach(() => {
      page.navigateToAdGroupTargets();
      page.openModal();
    });

    it('Nên hiển thị form fields cho keyword target', () => {
      page.assertKeywordInputExists();
      page.assertBidInputExists();
      page.assertMatchTypeDropdownExists();
    });

    it('Nên validate keyword bắt buộc', () => {
      page.clickAddButton();
      page.assertErrorMessage('Keyword is required');
    });

    it('Nên validate độ dài tối thiểu của keyword', () => {
      page.typeKeyword(validationTestData.invalidKeyword.tooShort);
      page.clickAddButton();
      page.assertErrorMessage('Keyword must be at least 2 characters long');
    });

    it('Nên validate bid bắt buộc', () => {
      page.typeKeyword(validationTestData.validKeyword.keyword);
      page.clickAddButton();
      page.assertErrorMessage('Bid is required');
    });

    it('Nên validate bid tối thiểu', () => {
      page.typeKeyword(validationTestData.validKeyword.keyword);
      page.typeBid(validationTestData.invalidBid.negative);
      page.clickAddButton();
      page.assertErrorMessage('Bid must be greater than 0');
    });

    it('Nên thêm keyword target thành công', () => {
      const testData = validationTestData.validKeyword;
      
      page.typeKeyword(testData.keyword);
      page.typeBid(testData.bid);
      page.selectMatchType(testData.matchType);
      
      page.clickAddButton();
      
      cy.wait('@addTarget').then((interception) => {
        expect(interception.request.body).to.include({
          keyword: testData.keyword,
          bid: testData.bid,
          match_type: 'EXACT'
        });
      });
      
      page.assertModalIsClosed();
    });
  });

  describe('Switch Component Integration Tests', () => {
    beforeEach(() => {
      page.navigateToAdGroupTargets();
    });

    it('Nên hiển thị switch cho mỗi target', () => {
      page.assertSwitchExists();
    });

    it('Nên hiển thị trạng thái đúng cho targets', () => {
      // Target đầu tiên (ENABLED) nên có switch bật
      page.assertSwitchIsEnabled(0);
      // Target thứ hai (PAUSED) nên có switch tắt  
      page.assertSwitchIsDisabled(1);
    });

    it('Nên toggle trạng thái target khi click switch', () => {
      page.getSwitchInTargetItem(0).then(($switch) => {
        const wasChecked = $switch.is(':checked');
        
        page.clickSwitch(0);
        
        cy.wait('@changeTargetStatus').then((interception) => {
          const expectedState = wasChecked ? 'PAUSED' : 'ENABLED';
          expect(interception.request.url).to.include(`state=${expectedState}`);
        });
        
        if (wasChecked) {
          page.assertSwitchIsDisabled(0);
        } else {
          page.assertSwitchIsEnabled(0);
        }
      });
    });

    it('Nên hiển thị loading state khi toggle switch', () => {
      // Mock slow API response
      cy.intercept('PUT', '/api/optimization/change_status_of_target*', (req) => {
        req.reply((res) => {
          res.delay(1000);
          res.send({ status: true, message: 'Target status updated' });
        });
      }).as('slowChangeTargetStatus');
      
      page.clickSwitch(0);
      
      // Switch nên bị disable trong lúc loading
      page.getSwitchInTargetItem(0).should('be.disabled');
      
      cy.wait('@slowChangeTargetStatus');
      
      // Switch nên được enable lại
      page.getSwitchInTargetItem(0).should('not.be.disabled');
    });
  });

  describe('Integration: AddTargetModal và Switch cùng hoạt động', () => {
    beforeEach(() => {
      page.navigateToAdGroupTargets();
    });

    it('Nên refresh target list sau khi add target thành công', () => {
      // Mở modal và add target
      page.openModal();
      
      const testData = validationTestData.validKeyword;
      page.typeKeyword(testData.keyword);
      page.typeBid(testData.bid);
      page.selectMatchType(testData.matchType);
      
      page.clickAddButton();
      
      cy.wait('@addTarget');
      
      // Target list nên được refresh
      cy.wait('@getTargets');
      
      // Modal nên đóng
      page.assertModalIsClosed();
    });

    it('Nên có thể toggle switch sau khi add target', () => {
      // Add target trước
      page.openModal();
      
      const testData = validationTestData.validKeyword;
      page.typeKeyword(testData.keyword);
      page.typeBid(testData.bid);
      page.selectMatchType(testData.matchType);
      
      page.clickAddButton();
      
      cy.wait('@addTarget');
      cy.wait('@getTargets');
      
      // Sau đó test switch functionality
      page.clickSwitch(0);
      cy.wait('@changeTargetStatus');
    });

    it('Nên hoạt động với cả keyword và product targets', () => {
      // Test với keyword target
      page.openModal();
      
      const keywordData = validationTestData.validKeyword;
      page.typeKeyword(keywordData.keyword);
      page.typeBid(keywordData.bid);
      page.selectMatchType(keywordData.matchType);
      
      page.clickAddButton();
      cy.wait('@addTarget');
      page.assertModalIsClosed();
      
      // Test switch cho keyword target
      page.getTargetListItem(0).should('contain', 'KEYWORD');
      page.clickSwitch(0);
      cy.wait('@changeTargetStatus');
      
      // Test switch cho product target
      page.getTargetListItem(1).should('contain', 'PRODUCT');
      page.clickSwitch(1);
      cy.wait('@changeTargetStatus');
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      page.navigateToAdGroupTargets();
    });

    it('Nên xử lý lỗi add target gracefully', () => {
      setupOptimizationSetsSwitchErrorMocks();
      
      page.openModal();
      
      const testData = validationTestData.validKeyword;
      page.typeKeyword(testData.keyword);
      page.typeBid(testData.bid);
      page.selectMatchType(testData.matchType);
      
      page.clickAddButton();
      
      cy.wait('@addTargetError');
      
      // Modal nên vẫn mở khi có lỗi
      page.assertModalIsOpen();
      page.assertApiError('Failed to add target');
    });

    it('Nên xử lý lỗi switch gracefully', () => {
      setupOptimizationSetsSwitchErrorMocks();
      
      page.getSwitchInTargetItem(0).then(($switch) => {
        const initialState = $switch.is(':checked');
        
        page.clickSwitch(0);
        
        cy.wait('@changeTargetStatusError');
        
        // Switch nên trở về trạng thái ban đầu
        if (initialState) {
          page.assertSwitchIsEnabled(0);
        } else {
          page.assertSwitchIsDisabled(0);
        }
      });
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      page.navigateToAdGroupTargets();
    });

    it('Add Target button nên có thể truy cập bằng bàn phím', () => {
      cy.get(page.navigation.addTargetButton).focus();
      cy.get(page.navigation.addTargetButton).should('be.focused');
      
      // Có thể mở modal bằng Enter
      cy.get(page.navigation.addTargetButton).type('{enter}');
      page.assertModalIsOpen();
    });

    it('Switch nên có thể truy cập bằng bàn phím', () => {
      page.getSwitchInTargetItem(0).focus();
      page.getSwitchInTargetItem(0).should('be.focused');
      
      // Có thể toggle bằng space
      page.getSwitchInTargetItem(0).type(' ');
      cy.wait('@changeTargetStatus');
    });

    it('Modal nên có thể đóng bằng Escape', () => {
      page.openModal();
      
      // Đóng modal bằng Escape
      cy.get('body').type('{esc}');
      page.assertModalIsClosed();
    });
  });

  describe('Performance và UX', () => {
    beforeEach(() => {
      page.navigateToAdGroupTargets();
    });

    it('Nên hiển thị loading states appropriately', () => {
      // Mock slow add target API
      cy.intercept('POST', '/api/optimization/targets', (req) => {
        req.reply((res) => {
          res.delay(2000);
          res.send({ status: true });
        });
      }).as('slowAddTarget');
      
      page.openModal();
      
      const testData = validationTestData.validKeyword;
      page.typeKeyword(testData.keyword);
      page.typeBid(testData.bid);
      page.selectMatchType(testData.matchType);
      
      page.clickAddButton();
      
      // Nên hiển thị loading spinner
      page.assertLoadingSpinner();
      
      cy.wait('@slowAddTarget');
      
      // Loading nên biến mất
      page.assertNoLoadingSpinner();
      page.assertModalIsClosed();
    });

    it('Nên prevent double submission', () => {
      page.openModal();
      
      const testData = validationTestData.validKeyword;
      page.typeKeyword(testData.keyword);
      page.typeBid(testData.bid);
      page.selectMatchType(testData.matchType);
      
      // Click Add button nhiều lần nhanh
      page.clickAddButton();
      page.clickAddButton();
      page.clickAddButton();
      
      // Chỉ nên có 1 API call
      cy.wait('@addTarget');
      cy.get('@addTarget.all').should('have.length', 1);
    });
  });
});
