"use client"

import { Fragment, useEffect, useMemo, useState } from 'react'
import { useTranslations, useFormatter } from "next-intl"
import { Disclosure, Popover, PopoverButton, PopoverPanel, Transition } from '@headlessui/react'
import { AttributionWindowOption } from './attribution-window-select'
import {
  MaterialReactTable,
  useMaterialReactTable,
  type MRT_ColumnDef,
  MRT_SortingState,
  MRT_ColumnSizingState,
} from 'material-react-table'
import { Box } from '@mui/material'
import { ExclamationTriangleIcon, Square2StackIcon, AdjustmentsHorizontalIcon } from "@heroicons/react/20/solid"
import { Check } from "@/components/ui/check"
import { TruckCheck  } from '../ui/truck-check'
import { TruckRemove } from '../ui/truck-remove'
import { cn, copyTextToClipboard } from "@/utils/msc"
import { getCurrencyCodeFromMarketplace, useCurrencyFormatter } from "@/utils/currency"

export type AdvertisedProduct = {
  asin: string;
  skus: any[];
  classification_rank: string;
  classification_rank_title: string;
  ad_performance: any[];
  sp_performance: any;
  browser_page_views: number;
  ordered_product_sales_amount: number;
  total_order_items: string;
  units_ordered: number;
  lowest_price: number;
  sp_conv_rate: string;
  cost: number;
  ad_sales: number;
  total_ad_cost: number;
  total_ad_sales: number;
  units_sold_clicks: number;
  acos: string;
  clicks: number;
  impressions: number;
  click_through_rate: string;
  cost_per_click: string;
  ad_conv_rate: string;
  eligibility_status: string;
  fulfillment_channel: string;
  shipping_price: number;
  estimated_fee: number;
  total_fee: number;
  available_quantity: number;
  unfulfillable_quantity: number;
  reserved_quantity: number;
  inbound_quantity: number;
  merchant_inventory_quantity: number;
  return_quantity: number;
  return_sales: number;
  bestSellerRank: string;
  brand: string;
  category: string;
  createdDate: string;
  image: string;
  item_name: string;
  recommend_type: string;
  estimated_days_of_supply: number;
  campaign_budget_amount: string;
  campaign_budget_currency_code: string;
  campaign_budget_type: string;
  campaign_id: string;
  campaign_name: string;
  campaign_status: string;
  date: string;
  id: string;
  spend: string;
  sessions: number;
  buy_box_percentage: number;
  attributed_sales_same_sku: number;
  units_sold_same_sku: number;
  tacos: number;
  profit: number;
  sd_sales: number;
  sd_sales_promoted_clicks: number;
  sd_cost: number;
  sd_units_sold: number;
  sd_clicks: number;
  sd_impressions: number;
  sd_impressions_views: number;
  sd_cumulative_reach: number;
  sd_detail_page_views: number;
  sd_new_to_brand_detail_page_views: number;
  sd_new_to_brand_sales: number;
  sd_roas: number;
  sd_acos: number;
  sd_conv_rate: number;
  sd_view_click_through_rate: number;
  sd_new_to_brand_sales_rate: number;
  sd_impression_frequency_average: number;
  sd_viewability_rate: number;
  sd_new_to_brand_detail_page_view_rate: number;
  points?: number;
};

export default function PinnedColTable({
  data,
  selectedAttributionWindow,
  selectedProduct,
  setSelectedProduct,
  selectedMarketplace
}: {
  data: AdvertisedProduct[]
  selectedAttributionWindow: AttributionWindowOption
  selectedProduct: AdvertisedProduct | null
  setSelectedProduct: (product: AdvertisedProduct) => void
  selectedMarketplace: any
}) {
  const t = useTranslations('DashboardPage')
  const tc = useTranslations('component')
  const { formatCurrency } = useCurrencyFormatter()
  
  // 선택된 마켓플레이스를 기반으로 통화 코드 결정
  const currencyCode = useMemo(() => getCurrencyCodeFromMarketplace(selectedMarketplace), [selectedMarketplace])
  const totalPageviews = useMemo(() => {
    const totalPageviews = data.reduce((acc, row) => acc + row.browser_page_views, 0)
    return totalPageviews
  }, [data])
  const totalSales = useMemo(() => {
    const totalSales = data.reduce((acc, row) => acc + row.ordered_product_sales_amount, 0)
    return totalSales
  }, [data])
  const totalFee = useMemo(() => {
    const totalFee = data.reduce((acc, row) => acc + row.total_fee, 0)
    return totalFee
  }, [data])
  const totalUnitsSold = useMemo(() => {
    const totalUnitsSold = data.reduce((acc, row) => acc + row.units_ordered, 0)
    return totalUnitsSold
  }, [data])
  const totalUnitsReturned = useMemo(() => {
    const totalUnitsReturned = data.reduce((acc, row) => acc + row.return_quantity, 0)
    return totalUnitsReturned
  }, [data])
  const totalSalesReturned = useMemo(() => {
    const totalSalesReturned = data.reduce((acc, row) => acc + row.return_sales, 0)
    return totalSalesReturned
  }, [data])
  const totalSessions = useMemo(() => {
    const totalSessions = data.reduce((acc, row) => acc + row.sessions, 0)
    return totalSessions
  }, [data])
  const totalBuyBoxPercentage = useMemo(() => {
    const totalBuyBoxPercentage = data.reduce((acc, row) => acc + row.buy_box_percentage, 0)
    return totalBuyBoxPercentage
  }, [data])
  const totalAdCost = useMemo(() => {
    const totalAdCost = data.reduce((acc, row) => acc + row.total_ad_cost, 0)
    return totalAdCost
  }, [data])
  const totalAdSales = useMemo(() => {
    const totalAdSales = data.reduce((acc, row) => acc + row.total_ad_sales, 0)
    return totalAdSales
  }, [data])
  const totalSpCost = useMemo(() => {
    const totalSpCost = data.reduce((acc, row) => acc + row.cost, 0)
    return totalSpCost
  }, [data])
  const totalSpSales = useMemo(() => {
    const totalSpSales = data.reduce((acc, row) => acc + row.ad_sales, 0)
    return totalSpSales
  }, [data])
  const totalClicks = useMemo(() => {
    const totalClicks = data.reduce((acc, row) => acc + row.clicks, 0)
    return totalClicks
  }, [data])
  const totalImpressions = useMemo(() => {
    const totalImpressions = data.reduce((acc, row) => acc + row.impressions, 0)
    return totalImpressions
  }, [data])
  const totalAdUnitsSold = useMemo(() => {
    const totalAdUnitsSold = data.reduce((acc, row) => acc + row.units_sold_clicks, 0)
    return totalAdUnitsSold
  }, [data])
  const totalAttributedSalesSameSku = useMemo(() => {
    const totalAttributedSalesSameSku = data.reduce((acc, row) => acc + row.attributed_sales_same_sku, 0)
    return totalAttributedSalesSameSku
  }, [data])
  const totalUnitsSoldSameSku = useMemo(() => {
    const totalUnitsSoldSameSku = data.reduce((acc, row) => acc + row.units_sold_same_sku, 0)
    return totalUnitsSoldSameSku
  }, [data])
  const totalSdSales = useMemo(() => {
    const totalSdSales = data.reduce((acc, row) => acc + row.sd_sales, 0)
    return totalSdSales
  }, [data])
  const totalSdSalesPromotedClicks = useMemo(() => {
    const totalSdSalesPromotedClicks = data.reduce((acc, row) => acc + row.sd_sales_promoted_clicks, 0)
    return totalSdSalesPromotedClicks
  }, [data])
  const totalSdCost = useMemo(() => {
    const totalSdCost = data.reduce((acc, row) => acc + row.sd_cost, 0)
    return totalSdCost
  }, [data])
  const totalSdUnitsSold = useMemo(() => {
    const totalSdUnitsSold = data.reduce((acc, row) => acc + row.sd_units_sold, 0)
    return totalSdUnitsSold
  }, [data])
  const totalSdClicks = useMemo(() => {
    const totalSdClicks = data.reduce((acc, row) => acc + row.sd_clicks, 0)
    return totalSdClicks
  }, [data])
  const totalSdImpressions = useMemo(() => {
    const totalSdImpressions = data.reduce((acc, row) => acc + row.sd_impressions, 0)
    return totalSdImpressions
  }, [data])
  const totalSdImpressionsViews = useMemo(() => {
    const totalSdImpressionsViews = data.reduce((acc, row) => acc + row.sd_impressions_views, 0)
    return totalSdImpressionsViews
  }, [data])
  const totalSdCumulativeReach = useMemo(() => {
    const totalSdCumulativeReach = data.reduce((acc, row) => acc + row.sd_cumulative_reach, 0)
    return totalSdCumulativeReach
  }, [data])
  const totalSdDetailPageViews = useMemo(() => {
    const totalSdDetailPageViews = data.reduce((acc, row) => acc + row.sd_detail_page_views, 0)
    return totalSdDetailPageViews
  }, [data])
  const totalSdNewToBrandDetailPageViews = useMemo(() => {
    const totalSdNewToBrandDetailPageViews = data.reduce((acc, row) => acc + row.sd_new_to_brand_detail_page_views, 0)
    return totalSdNewToBrandDetailPageViews
  }, [data])
  const totalSdNewToBrandSales = useMemo(() => {
    const totalSdNewToBrandSales = data.reduce((acc, row) => acc + row.sd_new_to_brand_sales, 0)
    return totalSdNewToBrandSales
  }, [data])


  

  const [sorting, setSorting] = useState<MRT_SortingState>([
    {
      id: totalSales === 0 ? 'ad_sales' : 'ordered_product_sales_amount',
      desc: true
    }
  ])
  useEffect(() => {
    setSorting([
      {
        id: totalSales === 0 ? 'ad_sales' : 'ordered_product_sales_amount',
        desc: true
      }
    ])
  }, [data])
  const [columnVisibility, setColumnVisibility] = useState({
    // sp
    units_ordered: false,
    return_quantity: false,
    return_sales: false,
    sessions: false,
    buy_box_percentage: false,
    tacos: false,
    profit: false,
    // ad (sp)
    cost: false,
    ad_sales: false,
    sp_roas: false,
    units_sold_clicks: false,
    attributed_sales_same_sku: false,
    units_sold_same_sku: false,
    acos: false,
    impressions: false,
    clicks: false,
    click_through_rate: false,
    cost_per_click: false,
    ad_conv_rate: false,
    // ad (sd)
    sd_cost: false,
    sd_sales: false,
    sd_sales_promoted_clicks: false,
    sd_units_sold: false,
    sd_clicks: false,
    sd_impressions: false,
    sd_impressions_views: false,
    sd_cumulative_reach: false,
    sd_detail_page_views: false,
    sd_new_to_brand_detail_page_views: false,
    sd_new_to_brand_sales: false,
    sd_roas: false,
    sd_acos: false,
    sd_conv_rate: false,
    sd_view_click_through_rate: false,
    sd_new_to_brand_sales_rate: false,
    sd_impression_frequency_average: false,
    sd_viewability_rate: false,
    sd_new_to_brand_detail_page_view_rate: false,
  })
  const handleColumnCheckChange = (columnName: keyof typeof columnVisibility) => {
    setColumnVisibility((prev) => ({
      ...prev,
      [columnName]: !prev[columnName],
    }))
  }
  const [columnSizing, setColumnSizing] = useState<MRT_ColumnSizingState>({
    products: 540,
  })

  const columns = useMemo<MRT_ColumnDef<AdvertisedProduct>[]>(
    () => [
      // products
      {
        id: 'products',
        header: t("table.header.products"),
        enableResizing: true,
        muiTableHeadCellProps: {
          sx: {
            position: 'sticky',
            left: '60px',
            zIndex: 1,
            paddingTop: '0.35rem',
            paddingBottom: '0.35rem',
            paddingRight: '1.75rem',
            paddingLeft: '0rem',
            color: '#9CA3AF',
            backgroundColor: '#FCFCFCF7',
            boxShadow: '-4px 0 4px -4px rgba(97, 97, 97, 0.5) inset',
          },
        },
        columns: [
          {
            accessorFn: (row) => `${row.asin}`, //accessorFn used to join multiple data into a single cell
            id: 'asin', //id is still required when using accessorFn instead of accessorKey
            header: 'Product & Ad Info',
            enableResizing: false,
            Header: () => (
              <div className="flex items-center h-[62px] overflow-hidden">
                <div className="w-[800px] text-left">
                  {t("table.subHeader.productsAdInfo")}
                </div>
              </div>
            ),
            enableColumnPinning: true,
            enableSorting: false,
            size: 540,
            muiTableHeadCellProps: {
              sx: {
                paddingLeft: '1rem',
                color: '#9CA3AF',
              },
            },
            muiTableBodyCellProps: {
              sx: {
                paddingLeft: '0',
                color: '#6b7280',
              },
            },
            Cell: ({ renderedCellValue, row }) => (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0rem',
                  position: 'relative',
                  width: '100%'
                }}
              >
                <div className="w-full relative flex items-center gap-x-4 px-4 overflow-hidden">
                  { row.original.image
                  ? (<img src={row.original.image} loading="lazy" alt="Item Image" className="flex-shrink-0 w-10 h-10 rounded" />)
                  : (<div className="flex-shrink-0 flex items-center justify-center w-10 h-10 bg-gray-100 rounded">
                      <ExclamationTriangleIcon className="h-5 w-5 text-gray-300" />
                    </div>)
                  }
                  <div className="flex-1 flex flex-col gap-y-0.5 overflow-hidden">
                    <div
                      className="flex items-center gap-x-1"
                      title={row.original.item_name}
                    >
                      {row.original.recommend_type && (
                        row.original.recommend_type === "GROWTH"
                          ? <div className="flex-shrink-0 py-0.25 px-1.5 text-white bg-purple-500 text-[10px] rounded-md">Growth</div>
                          : row.original.recommend_type === "EFFICIENCY"
                            ? <div className="flex-shrink-0 py-0.25 px-1.5 text-white bg-green-500 text-[10px] rounded-md">Efficiency</div>
                            : ""
                      )}
                      <span className="text-xs text-gray-500 text-left font-semibold truncate">
                        {row.original.item_name
                          ? row.original.item_name
                          : (row.original.skus && row.original.skus.length > 0 && row.original.skus[0]?.sku)
                            ? row.original.skus[0].sku
                            : "No Title"
                        }
                      </span>
                    </div>
                    <div className="flex items-center gap-x-2 divide-x divide-gray-100">
                      <div className="text-xs text-red-400 font-semibold">{formatCurrency(row.original.lowest_price, currencyCode)}</div>
                      <div className="flex items-center pl-2 text-xs font-semibold">
                        {row.original.fulfillment_channel === "DEFAULT"
                          ? <TruckRemove className="w-4 h-4 text-gray-400"/>
                          : <TruckCheck className="w-4 h-4 text-orange-300"/>
                        }
                        {row.original.fulfillment_channel === "DEFAULT"
                          ? <div className="pl-1 text-gray-400 font-semibold">FBM</div>
                          : <div className="pl-1 text-orange-400 font-semibold">{formatCurrency(row.original.shipping_price, currencyCode)}</div>
                        }
                      </div>
                      <div
                        className={cn(
                          "pl-2 text-xs font-semibold",
                          row.original.eligibility_status === "ELIGIBLE" ? "text-blue-400" : "text-gray-400"
                        )}
                      >
                        {row.original.eligibility_status === "ELIGIBLE"
                          ? tc("eligibility.eligible")
                          : tc("eligibility.ineligible")
                        }
                      </div>
                    </div>
                    <div className="mt-1 flex items-center gap-x-2 divide-x divide-gray-100">
                      {/* <div className="text-[10px] text-gray-400"><span className="font-semibold">#{row.original.classification_rank} </span>in {row.original.classification_rank_title}</div> */}
                      {t.rich("table.content.products.rank",{
                          value1: row.original.classification_rank,
                          value2: row.original.classification_rank_title,
                          first: (chunks) => <div className="text-[10px] text-gray-400">{chunks}</div>,
                          second: (chunks) => <span className="font-semibold">{chunks}</span>
                        }
                      )}
                      <div className="pl-2 text-[10px] text-gray-400"><span className="font-semibold">{t("table.content.products.fee")}: </span>{formatCurrency(row.original.estimated_fee, currencyCode)}</div>
                      {row.original.points != null && (
                        <div className="pl-2 text-[10px] text-gray-400"><span className="font-semibold">{t("table.content.products.points")}: </span>{row.original.points.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
                      )}
                    </div>
                    <div className="flex items-center gap-x-1 text-[10px] text-gray-400">
                      <span className="font-semibold">{t("table.content.products.inventory")}: </span>
                      <div className="truncate">
                        <div className="flex items-center gap-x-1">
                          { row.original.fulfillment_channel === "DEFAULT" ?
                            <>{row.original.merchant_inventory_quantity}</>
                            :
                            <>
                              <div>{row.original.available_quantity} {t("table.content.products.inventoryType.available")},</div>
                              <div>{row.original.unfulfillable_quantity} {t("table.content.products.inventoryType.unfulfillable")},</div>
                              <div>{row.original.reserved_quantity} {t("table.content.products.inventoryType.reserved")},</div>
                              <div>{row.original.inbound_quantity} {t("table.content.products.inventoryType.inbound")}</div>
                            </>
                          }
                        </div>
                      </div>
                      {row.original.fulfillment_channel !== "DEFAULT" && row.original.estimated_days_of_supply && typeof row.original.estimated_days_of_supply === "number" && (
                        row.original.estimated_days_of_supply <= 30
                          ? (
                            <div
                              className="flex-shrink-0 py-0.25 px-1.5 text-white bg-red-500 text-[10px] rounded-md"
                              title={`This item will run out of stock in ${row.original.estimated_days_of_supply} days.`}
                            >
                              Low Stock
                            </div>
                          )
                          : row.original.estimated_days_of_supply >= 180
                            ? <div
                                className="flex-shrink-0 py-0.25 px-1.5 text-white bg-cyan-500 text-[10px] rounded-md"
                                title={`This item has more than ${row.original.estimated_days_of_supply} days of supply.`}
                              >
                                Excess Stock
                              </div>
                            : ""
                      )}
                    </div>
                    <div className="flex items-center gap-x-2 divide-x divide-gray-100">
                      <div
                        className="group flex items-center gap-x-1 text-[10px] text-gray-400"
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          copyTextToClipboard(row.original.asin)
                        }}
                      >
                        <span className="font-semibold">{t("table.content.products.asin")}: </span>{row.original.asin}
                        <Square2StackIcon className="w-3 h-3 text-gray-200 group-hover:text-gray-300"/>
                      </div>
                      <div className="pl-2 text-[10px] text-gray-400 truncate"><span className="font-semibold">{t("table.content.products.sku")}: </span>
                        {row.original.skus.map((sku, index) => (
                          <span key={sku.sku}>
                            {index > 0 && ", "}
                            {sku.sku}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                {/* using renderedCellValue instead of cell.getValue() preserves filter match highlighting */}
              </Box>
            ),
          },
        ],
      },
      // sp
      {
        id: 'sp',
        header: 'Total Performance',
        enableResizing: false,
        Header: () => (
          <div className="py-2">
            {t("table.header.totalPerformance")}
          </div>
        ),
        muiTableHeadCellProps: {
          sx: {
            paddingTop: '0.35rem',
            paddingBottom: '0.35rem',
            color: 'rgba(88, 28, 135, 0.6)',
            borderRight: '1px solid #e5e7eb',
          },
        },
        columns: [
          {
            accessorKey: 'total_fee',
            header: 'Est. Fees',
            enableResizing: false,
            enableColumnPinning: true,
            size: 160,
            Cell: ({ cell }) => <span className="inline-block text-right">{formatCurrency(parseFloat(cell.getValue<string>()), currencyCode)}</span>,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.totalMetrics.estFee")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {formatCurrency(totalFee, currencyCode)}
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'ordered_product_sales_amount',
            header: 'Sales',
            enableResizing: false,
            enableColumnPinning: true,
            size: 160,
            Cell: ({ cell }) => <span className="inline-block text-right">{formatCurrency(parseFloat(cell.getValue<string>()), currencyCode)}</span>,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.totalMetrics.sales")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {formatCurrency(totalSales, currencyCode)}
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'units_ordered',
            header: 'Units Sold',
            enableResizing: false,
            enableColumnPinning: true,
            size: 130,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.totalMetrics.unitsSold")}</div>
                <div className="text-base text-gray-500 font-bold">{totalUnitsSold.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'return_quantity',
            header: 'Units Returned',
            enableResizing: false,
            enableColumnPinning: true,
            size: 160,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.totalMetrics.unitsReturned")}</div>
                <div className="text-base text-gray-500 font-bold">{totalUnitsReturned.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'return_sales',
            header: 'Sales Returned',
            enableResizing: false,
            enableColumnPinning: true,
            size: 160,
            Cell: ({ cell }) => <span className="inline-block text-right">{formatCurrency(parseFloat(cell.getValue<string>()), currencyCode)}</span>,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.totalMetrics.salesReturned")}</div>
                <div className="text-base text-gray-500 font-bold">{formatCurrency(totalSalesReturned, currencyCode)}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sessions',
            header: 'Sessions',
            enableResizing: false,
            enableColumnPinning: true,
            size: 130,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.totalMetrics.sessions")}</div>
                <div className="text-base text-gray-500 font-bold">{totalSessions.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'buy_box_percentage',
            header: 'Buy Box Percent',
            enableResizing: false,
            enableColumnPinning: true,
            size: 200,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.totalMetrics.buyBoxPercentage")}</div>
                <div className="text-base text-gray-500 font-bold">{totalBuyBoxPercentage.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'browser_page_views',
            header: 'Pageviews',
            enableResizing: false,
            enableColumnPinning: true,
            size: 140,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.totalMetrics.pageViews")}</div>
                <div className="text-base text-gray-500 font-bold">{totalPageviews.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sp_conv_rate',
            header: 'CVR (Total)',
            enableResizing: false,
            enableColumnPinning: true,
            size: 140,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.totalMetrics.cvrTotal")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalPageviews === 0
                    ? '-'
                    : ((totalUnitsSold / totalPageviews) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'tacos',
            header: 'TACOS',
            enableResizing: false,
            enableColumnPinning: true,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            size: 120,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.tacos")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalSales === 0
                    ? '-'
                    : (((totalSpCost + totalSdCost) / totalSales) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'profit',
            header: 'Profit',
            enableResizing: false,
            enableColumnPinning: true,
            Cell: ({ cell }) => <span className="inline-block text-right">{formatCurrency(parseFloat(cell.getValue<string>()), currencyCode)}</span>,
            size: 120,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.profit")}</div>
                <div className="text-base text-gray-500 font-bold">{formatCurrency(totalSales - totalFee - totalAdCost - totalSdCost, currencyCode)}</div>
              </div>
            ),
          },
        ],
      },
      // ads
      {
        id: 'ad',
        header: 'Ad Performance',
        enableResizing: false,
        Header: () => (
          <div className="py-2">
            {t("table.header.adPerformance")}
          </div>
        ),
        muiTableHeadCellProps: {
          sx: {
            paddingTop: '0.35rem',
            paddingBottom: '0.35rem',
            color: 'rgba(30, 58, 138, 0.6)',
          },
        },
        columns: [
          {
            accessorKey: 'total_ad_cost',
            header: 'Ad Spend',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{formatCurrency(parseFloat(cell.getValue<string>()), currencyCode)}</span>,
            size: 150,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.adSpend")}</div>
                <div className="text-base text-gray-500 font-bold">{formatCurrency(totalAdCost, currencyCode)}</div>
              </div>
            ),
          },
          {
            accessorKey: 'total_ad_sales',
            header: 'Ad Sales',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{formatCurrency(parseFloat(cell.getValue<string>()), currencyCode)}</span>,
            size: 160,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.adSales")}</div>
                <div className="text-base text-gray-500 font-bold">{formatCurrency(totalAdSales, currencyCode)}</div>
              </div>
            ),
          },
          {
            accessorKey: 'roas',
            header: 'ROAS',
            enableResizing: false,
            enableColumnPinning: true,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            size: 140,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.roas")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalAdCost === 0
                    ? '-'
                    : ((totalAdSales / totalAdCost) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'cost',
            header: 'SP Spend',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{formatCurrency(parseFloat(cell.getValue<string>()), currencyCode)}</span>,
            size: 150,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.spCost")}</div>
                <div className="text-base text-gray-500 font-bold">{formatCurrency(totalSpCost, currencyCode)}</div>
              </div>
            ),
          },
          {
            accessorKey: 'ad_sales',
            header: 'SP Sales',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{formatCurrency(parseFloat(cell.getValue<string>()), currencyCode)}</span>,
            size: 150,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.spSales")}</div>
                <div className="text-base text-gray-500 font-bold">{formatCurrency(totalSpSales, currencyCode)}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sp_roas',
            header: 'SP ROAS',
            enableResizing: false,
            enableColumnPinning: true,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            size: 120,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.spRoas")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalSpCost === 0
                    ? '-'
                    : ((totalSpSales / totalSpCost) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'units_sold_clicks',
            header: 'SP Units Sold',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            size: 150,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.adUnitsSold")}</div>
                <div className="text-base text-gray-500 font-bold">{totalAdUnitsSold.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'attributed_sales_same_sku',
            header: 'SP Attributed Sales (Same SKU)',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{formatCurrency(parseFloat(cell.getValue<string>()), currencyCode)}</span>,
            size: 280,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.attributedSalesSameSku")}</div>
                <div className="text-base text-gray-500 font-bold">{formatCurrency(totalAttributedSalesSameSku, currencyCode)}</div>
              </div>
            ),
          },
          {
            accessorKey: 'units_sold_same_sku',
            header: 'SP Units Sold (Same SKU)',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            size: 240,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.unitsSoldSameSku")}</div>
                <div className="text-base text-gray-500 font-bold">{totalUnitsSoldSameSku.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'acos',
            header: 'SP ACOS',
            enableResizing: false,
            enableColumnPinning: true,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            size: 120,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.spAcos")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalSpSales === 0
                    ? '-'
                    : ((totalSpCost / totalSpSales) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'impressions',
            header: 'SP Impressions',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            size: 140,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.spImpressions")}</div>
                <div className="text-base text-gray-500 font-bold">{totalImpressions.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'clicks',
            header: 'SP Clicks',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            size: 120,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.spClicks")}</div>
                <div className="text-base text-gray-500 font-bold">{totalClicks.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'click_through_rate',
            header: 'SP CTR',
            enableResizing: false,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            size: 100,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.spCtr")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalImpressions === 0
                    ? '-'
                    : ((totalClicks / totalImpressions) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'cost_per_click',
            header: 'SP Avg. CPC',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{formatCurrency(parseFloat(cell.getValue<string>()), currencyCode)}</span>,
            size: 130,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.spAvgCPC")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalClicks === 0
                    ? '-'
                    : formatCurrency(totalSpCost / totalClicks, currencyCode)}
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'ad_conv_rate',
            header: 'SP CVR',
            enableResizing: false,
            enableColumnPinning: true,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            size: 130,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.spCvr")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalClicks === 0
                    ? '-'
                    : ((totalAdUnitsSold / totalClicks) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_cost',
            header: 'SD Cost',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{formatCurrency(parseFloat(cell.getValue<string>()), currencyCode)}</span>,
            size: 150,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdCost")}</div>
                <div className="text-base text-gray-500 font-bold">{formatCurrency(totalSdCost, currencyCode)}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_sales',
            header: 'SD Sales',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{formatCurrency(parseFloat(cell.getValue<string>()), currencyCode)}</span>,
            size: 150,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdSales")}</div>
                <div className="text-base text-gray-500 font-bold">{formatCurrency(totalSdSales, currencyCode)}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_sales_promoted_clicks',
            header: 'SD Sales Promoted Clicks',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            size: 200,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdSalesPromotedClicks")}</div>
                <div className="text-base text-gray-500 font-bold">{totalSdSalesPromotedClicks.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_units_sold',
            header: 'SD Units Sold',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            size: 150,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdUnitsSold")}</div>
                <div className="text-base text-gray-500 font-bold">{totalSdUnitsSold.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_clicks',
            header: 'SD Clicks',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            size: 130,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdClicks")}</div>
                <div className="text-base text-gray-500 font-bold">{totalSdClicks.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_impressions',
            header: 'SD Impressions',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            size: 150,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdImpressions")}</div>
                <div className="text-base text-gray-500 font-bold">{totalSdImpressions.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_impressions_views',
            header: 'SD Impressions Views',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            size: 200,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdImpressionsViews")}</div>
                <div className="text-base text-gray-500 font-bold">{totalSdImpressionsViews.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_cumulative_reach',
            header: 'SD Cumulative Reach',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            size: 200,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdCumulativeReach")}</div>
                <div className="text-base text-gray-500 font-bold">{totalSdCumulativeReach.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_detail_page_views',
            header: 'SD Detail Page Views',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            size: 200,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdDetailPageViews")}</div>
                <div className="text-base text-gray-500 font-bold">{totalSdDetailPageViews.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_new_to_brand_detail_page_views',
            header: 'SD New to Brand Detail Page Views',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{parseFloat(cell.getValue<string>()).toLocaleString(undefined, {maximumFractionDigits: 0})}</span>,
            size: 250,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdNewToBrandDetailPageViews")}</div>
                <div className="text-base text-gray-500 font-bold">{totalSdNewToBrandDetailPageViews.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_new_to_brand_sales',
            header: 'SD New to Brand Sales',
            enableResizing: false,
            Cell: ({ cell }) => <span className="inline-block text-right">{formatCurrency(parseFloat(cell.getValue<string>()), currencyCode)}</span>,
            size: 200,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdNewToBrandSales")}</div>
                <div className="text-base text-gray-500 font-bold">{formatCurrency(totalSdNewToBrandSales, currencyCode)}</div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_roas',
            header: 'SD ROAS',
            enableResizing: false,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            size: 150,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdRoas")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalSdCost === 0
                    ? '-'
                    : ((totalSdSales / totalSdCost) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_acos',
            header: 'SD ACOS',
            enableResizing: false,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            size: 150,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdAcos")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalSdSales === 0
                    ? '-'
                    : ((totalSdCost / totalSdSales) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_conv_rate',
            header: 'SD CVR',
            enableResizing: false,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            size: 130,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdConvRate")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalSdClicks === 0
                    ? '-'
                    : ((totalSdUnitsSold / totalSdClicks) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_view_click_through_rate',
            header: 'SD View CTR',
            enableResizing: false,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            size: 150,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdViewClickThroughRate")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalSdImpressionsViews === 0
                    ? '-'
                    : ((totalSdClicks / totalSdImpressionsViews) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_new_to_brand_sales_rate',
            header: 'SD New to Brand Sales Rate',
            enableResizing: false,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            size: 200,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdNewToBrandSalesRate")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalSdSales === 0
                    ? '-'
                    : ((totalSdNewToBrandSales / totalSdSales) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_impression_frequency_average',
            header: 'SD Impression Frequency Average',
            enableResizing: false,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : value.toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}</span>;
            },
            size: 200,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdImpressionFrequencyAverage")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalSdCumulativeReach === 0
                    ? '-'
                    : ((totalSdImpressions / totalSdCumulativeReach).toFixed(4))}
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_viewability_rate',
            header: 'SD Viewability Rate',
            enableResizing: false,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            size: 150,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdViewabilityRate")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalSdImpressions === 0
                    ? '-'
                    : ((totalSdImpressionsViews / totalSdImpressions) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          },
          {
            accessorKey: 'sd_new_to_brand_detail_page_view_rate',
            header: 'SD New to Brand Detail Page View Rate',
            enableResizing: false,
            Cell: ({ cell }) => {
              const value = parseFloat(cell.getValue<string>());
              return <span className="inline-block text-right">{isNaN(value) ? '-' : (value * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2}) + '%'}</span>;
            },
            size: 250,
            Header: () => (
              <div className="py-2 pl-2 text-right">
                <div>{t("table.content.adMetrics.sdNewToBrandDetailPageViewRate")}</div>
                <div className="text-base text-gray-500 font-bold">
                  {totalSdImpressions === 0
                    ? '-'
                    : ((totalSdNewToBrandDetailPageViews / totalSdImpressions) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})}%
                </div>
              </div>
            ),
          }
        ],
      },
    ],
    [data, columnVisibility],
  )

  const table = useMaterialReactTable({
    columns,
    data,
    state: { columnVisibility, sorting, columnSizing },
    onSortingChange: setSorting,
    onColumnSizingChange: (func) => {
      let newColumnSize = typeof func === 'function' ? func({}) : {}
      if (newColumnSize['products'] && newColumnSize['products'] > 200 && newColumnSize['products'] < 800) {
        setColumnSizing(newColumnSize)
      }
    },
    columnResizeMode: 'onEnd',
    enableTopToolbar: false,
    enableStickyHeader: true,
    enableColumnPinning: true,
    enableColumnActions: false,
    enableColumnResizing: true,
    enableSorting: true,
    layoutMode: 'grid-no-grow',
    initialState: {
      columnPinning: { left: ['mrt-row-expand', 'asin', 'fee', 'products'] },
    },
    enableExpandAll: false, //disable expand all button
    displayColumnDefOptions: {
      'mrt-row-expand': {
        muiTableHeadCellProps: {
          sx: {
            paddingLeft: '1.5rem',
            paddingRight: '0.5rem',
          },
        },
        muiTableBodyCellProps: ({row}) => ({
          sx: {
            paddingLeft: '1.5rem',
            paddingRight: '0.5rem',
          },
        }),
        header: '',
        enableResizing: false,
        size: 60,
      },
    },
    muiDetailPanelProps: () => ({
      sx: (theme) => ({
        backgroundColor: 'rgba(243, 244, 246, 0.75)',
        padding: '0',
      }),
    }),
    //custom expand button rotation
    muiExpandButtonProps: ({ row, table }) => ({
      onClick: () => table.setExpanded({ [row.id]: !row.getIsExpanded() }), //only 1 detail panel open at a time
      sx: {
        transform: row.getIsExpanded() ? 'rotate(180deg)' : 'rotate(-90deg)',
        transition: 'transform 0.2s',
      },
    }),
    //conditionally render detail panel
    renderDetailPanel: ({ row }) =>
      row.original.ad_performance.length > 0 ? (
        <div className="relative flex items-center">
          <div
            className="sticky left-0 z-[1] overflow-hidden"
            style={{
              width: (columnSizing.products + 60) + 'px',
            }}
          >
            <div className="relative flex items-center h-8 py-2 text-left text-xs text-gray-400 font-bold bg-gray-50">
              <div className="flex-shrink-0 w-[140px] pl-8">
                {t("table.adDetailRow.header.status")}
              </div>
              <div className="flex-shrink-0 w-[110px] px-8">
                {t("table.adDetailRow.header.runBy")}
              </div>
              <div className="grow px-8">
                {t("table.adDetailRow.header.campaign")}
              </div>
            </div>
            {/* detail panel list section */}
            <ul className="divide-y divide-gray-100 bg-white">
              {row.original.ad_performance.map((ad: any) => (
                <li className="relative flex items-center h-[70px] py-4 cursor-pointer text-left text-gray-500 text-sm" key={row.original.asin + "1"}>
                  <div className="flex-shrink-0 flex items-center justify-start gap-x-2 w-[140px] pl-8">
                    <div
                      className={cn(
                        "h-2 w-2 rounded-full",
                        ad.state === "ENABLED"
                          ? "bg-green-500"
                          : ad.state === "PAUSED"
                            ? "bg-yellow-500"
                            : "bg-red-500"
                      )}
                    />
                    {ad.state === "ENABLED"
                      ? tc("adStatus.enabled")
                      : ad.state === "PAUSED"
                        ? tc("adStatus.paused")
                        : ad.state
                    }
                    {/* {ad.state} */}
                  </div>
                  <div className="flex-shrink-0 flex items-center justify-start gap-x-2 w-[110px] px-8">
                    <span
                      className={cn(
                        " font-bold",
                        ad.mop_yn === "Y"
                          ? "text-blue-500"
                          : "text-orange-400"
                      )}
                    >
                      {
                        ad.mop_yn === "Y"
                          ? "Optapex"
                          : "Amazon"
                      }
                    </span>
                  </div>
                  <div className="grow relative flex items-center gap-x-4 px-8 overflow-hidden">
                    <div className="w-full flex flex-col overflow-hidden">
                      <div className="text-xs text-gray-500 text-left font-semibold truncate">
                        {ad.name}
                      </div>
                      <div className="flex items-center gap-x-2 divide-x divide-gray-100">
                        <div className="flex-shrink-0 text-[10px] text-gray-400">{t("table.adDetailRow.content.campaign.id")}: {ad.campaign_id}</div>
                        <div className="flex-shrink-0 pl-2 text-[10px] text-gray-400">{t("table.adDetailRow.content.campaign.budget")}: {formatCurrency(parseFloat(ad.budget_amount || '0'), currencyCode)}</div>
                        <div className="flex-shrink-0 pl-2 text-[10px] text-gray-400 font-semibold">{ad.targeting_settings}</div>
                      </div>
                    </div>
                  </div>
                </li>
              ))
              }
            </ul>
            <div
              style={{
                boxShadow: '-4px 0 4px -4px rgba(97, 97, 97, 0.5) inset',
                content: '',
                height: '100%',
                left: '0',
                position: 'absolute',
                top: '0',
                width: (columnSizing.products + 60) + 'px',
              }}></div>
          </div>
          <div className="bg-white">
            <div className="flex items-center h-8 py-2 text-left text-xs text-gray-400 font-bold bg-gray-100/40">
            </div>
            <ul className="divide-y divide-gray-100">
              {row.original.ad_performance.sort((a, b) => b.sales7d - a.sales7d).map((ad: any) => {
                const adSales = selectedAttributionWindow.type === '1d'
                  ? ad.sales1d
                  : selectedAttributionWindow.type === '7d'
                    ? ad.sales7d
                    : selectedAttributionWindow.type === '14d'
                      ? ad.sales14d
                      : ad.sales30d
                const unitsSoldClicks = selectedAttributionWindow.type === '1d'
                  ? ad.units_sold_clicks1d
                  : selectedAttributionWindow.type === '7d'
                    ? ad.units_sold_clicks7d
                    : selectedAttributionWindow.type === '14d'
                      ? ad.units_sold_clicks14d
                      : ad.units_sold_clicks30d
                const attributedSalesSameSku = selectedAttributionWindow.type === '1d'
                  ? ad.attributed_sales_same_sku1d
                  : selectedAttributionWindow.type === '7d'
                    ? ad.attributed_sales_same_sku7d
                    : selectedAttributionWindow.type === '14d'
                      ? ad.attributed_sales_same_sku14d
                      : ad.attributed_sales_same_sku30d
                const unitsSoldSameSku = selectedAttributionWindow.type === '1d'
                  ? ad.units_sold_same_sku1d
                  : selectedAttributionWindow.type === '7d'
                    ? ad.units_sold_same_sku7d
                    : selectedAttributionWindow.type === '14d'
                      ? ad.units_sold_same_sku14d
                      : ad.units_sold_same_sku30d
                return (
                <li className="relative flex items-center h-[70px] py-8 cursor-pointer text-right text-gray-500 text-sm" key={row.original.asin + "1"}>
                  {/* null sp section */}
                  <div className="flex-shrink-0 w-[180px] pl-8 pr-4">
                    -
                  </div>
                  <div className="flex-shrink-0 w-[150px] px-4">
                    -
                  </div>
                  {columnVisibility.units_ordered &&
                  <div className="flex-shrink-0 w-[130px] px-4">
                    -
                  </div>
                  }
                  {columnVisibility.return_quantity &&
                  <div className="flex-shrink-0 w-[160px] px-4">
                    -
                  </div>
                  }
                  {columnVisibility.return_sales &&
                  <div className="flex-shrink-0 w-[160px] px-4">
                    -
                  </div>
                  }
                  {columnVisibility.sessions &&
                  <div className="flex-shrink-0 w-[130px] px-4">
                    -
                  </div>
                  }
                  {columnVisibility.buy_box_percentage &&
                  <div className="flex-shrink-0 w-[200px] px-4">
                    -
                  </div>
                  }
                  <div className="flex-shrink-0 w-[130px] px-4">
                    -
                  </div>
                  <div className="flex-shrink-0 w-[140px] px-4">
                    -
                  </div>
                  {columnVisibility.tacos &&
                  <div className="flex-shrink-0 w-[120px] px-4">
                    -
                  </div>
                  }
                  {columnVisibility.profit &&
                  <div className="flex-shrink-0 w-[120px] px-4">
                    -
                  </div>
                  }
                  {/* ad section */}
                  <div className="flex-shrink-0 w-[150px] px-4">
                    {formatCurrency(parseFloat((ad.cost + ad.sd_cost)), currencyCode)}
                  </div>
                  <div className="flex-shrink-0 w-[150px] px-4">
                    {formatCurrency(parseFloat(adSales + ad.sd_sales), currencyCode)}
                  </div>
                  <div className="flex-shrink-0 w-[120px] px-4">
                    {
                      (ad.cost + ad.sd_cost) === 0
                        ? '-'
                        : ((parseFloat(adSales + ad.sd_sales) / parseFloat((ad.cost + ad.sd_cost))) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})
                    }%
                  </div>
                  {columnVisibility.ad_sales &&
                  <div className="flex-shrink-0 w-[150px] px-4">
                    {formatCurrency(parseFloat(adSales), currencyCode)}
                  </div>
                  }
                  {columnVisibility.cost &&
                  <div className="flex-shrink-0 w-[150px] px-4">
                    {formatCurrency(parseFloat(ad.cost), currencyCode)}
                  </div>
                  }
                  {columnVisibility.sp_roas &&
                  <div className="flex-shrink-0 w-[120px] px-4">
                    {
                      ad.cost === 0
                        ? '-'
                        : ((parseFloat(adSales) / parseFloat(ad.cost)) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})
                    }%
                  </div>
                  }
                  {columnVisibility.units_sold_clicks &&
                  <div className="flex-shrink-0 w-[150px] px-4">
                    {unitsSoldClicks}
                  </div>
                  }
                  {columnVisibility.attributed_sales_same_sku &&
                  <div className="flex-shrink-0 w-[280px] px-4">
                    {attributedSalesSameSku}
                  </div>
                  }
                  {columnVisibility.units_sold_same_sku &&
                  <div className="flex-shrink-0 w-[240px] px-4">
                    {unitsSoldSameSku}
                  </div>
                  }
                  {columnVisibility.acos &&
                  <div className="flex-shrink-0 w-[120px] px-4">
                    {
                      adSales === 0
                        ? '-'
                        : ((parseFloat(ad.cost) / parseFloat(adSales)) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})
                    }%
                  </div>
                  }
                  {columnVisibility.impressions &&
                  <div className="flex-shrink-0 w-[140px] px-4">
                    {ad.impressions.toLocaleString(undefined, {maximumFractionDigits: 0})}
                  </div>
                  }
                  {columnVisibility.clicks &&
                  <div className="flex-shrink-0 w-[120px] px-4">
                    {ad.clicks.toLocaleString(undefined, {maximumFractionDigits: 0})}
                  </div>
                  }
                  {columnVisibility.click_through_rate &&
                  <div className="flex-shrink-0 w-[100px] px-4">
                    {
                      ad.impressions === 0
                      ? '-'
                      : ((parseInt(ad.clicks) / parseInt(ad.impressions)) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})
                    }%
                  </div>
                  }
                  {columnVisibility.cost_per_click &&
                  <div className="flex-shrink-0 w-[130px] px-4">
                    {
                      ad.clicks === 0
                      ? '-'
                      : formatCurrency((parseFloat(ad.cost) / parseInt(ad.clicks)), currencyCode)
                    }
                  </div>
                  }
                  {columnVisibility.ad_conv_rate &&
                  <div className="flex-shrink-0 w-[130px] px-4">
                    {
                      ad.clicks === 0
                      ? '-'
                      : ((parseInt(unitsSoldClicks) / parseInt(ad.clicks)) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})
                    }%
                  </div>
                  }
                  {columnVisibility.sd_cost &&
                  <div className="flex-shrink-0 w-[150px] px-4">
                    {formatCurrency(parseFloat(ad.sd_cost), currencyCode)}
                  </div>
                  }
                  {columnVisibility.sd_sales &&
                  <div className="flex-shrink-0 w-[150px] px-4">
                    {formatCurrency(parseFloat(ad.sd_sales), currencyCode)}
                  </div>
                  }
                  {columnVisibility.sd_sales_promoted_clicks &&
                  <div className="flex-shrink-0 w-[200px] px-4">
                    {ad.sd_sales_promoted_clicks}
                  </div>
                  }
                  {columnVisibility.sd_units_sold &&
                  <div className="flex-shrink-0 w-[150px] px-4">
                    {ad.sd_units_sold}
                  </div>
                  }
                  {columnVisibility.sd_clicks &&
                  <div className="flex-shrink-0 w-[130px] px-4">
                    {ad.sd_clicks}
                  </div>
                  }
                  {columnVisibility.sd_impressions &&
                  <div className="flex-shrink-0 w-[150px] px-4">
                    {ad.sd_impressions}
                  </div>
                  }
                  {columnVisibility.sd_impressions_views &&
                  <div className="flex-shrink-0 w-[200px] px-4">
                    {ad.sd_impressions_views}
                  </div>
                  }
                  {columnVisibility.sd_cumulative_reach &&
                  <div className="flex-shrink-0 w-[200px] px-4">
                    {ad.sd_cumulative_reach}
                  </div>
                  }
                  {columnVisibility.sd_detail_page_views &&
                  <div className="flex-shrink-0 w-[200px] px-4">
                    {ad.sd_detail_page_views}
                  </div>
                  }
                  {columnVisibility.sd_new_to_brand_detail_page_views &&
                  <div className="flex-shrink-0 w-[250px] px-4">
                    {ad.sd_new_to_brand_detail_page_views}
                  </div>
                  }
                  {columnVisibility.sd_new_to_brand_sales &&
                  <div className="flex-shrink-0 w-[200px] px-4">
                    {formatCurrency(parseFloat(ad.sd_new_to_brand_sales), currencyCode)}
                  </div>
                  }
                  {columnVisibility.sd_roas &&
                  <div className="flex-shrink-0 w-[150px] px-4">
                    {
                      ad.sd_cost === 0
                        ? '-'
                        : ((parseFloat(ad.sd_sales) / parseFloat(ad.sd_cost)) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})
                    }%
                  </div>
                  }
                  {columnVisibility.sd_acos &&
                  <div className="flex-shrink-0 w-[150px] px-4">
                    {
                      ad.sd_sales === 0
                        ? '-'
                        : ((parseFloat(ad.sd_cost) / parseFloat(ad.sd_sales)) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})
                    }%
                  </div>
                  }
                  {columnVisibility.sd_conv_rate &&
                  <div className="flex-shrink-0 w-[130px] px-4">
                    {
                      ad.sd_clicks === 0
                        ? '-'
                        : ((parseInt(ad.sd_units_sold) / parseInt(ad.sd_clicks)) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})
                    }%
                  </div>
                  }
                  {columnVisibility.sd_view_click_through_rate &&
                  <div className="flex-shrink-0 w-[150px] px-4">
                    {
                      ad.sd_impressions_views === 0
                        ? '-'
                        : ((parseInt(ad.sd_clicks) / parseInt(ad.sd_impressions_views)) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})
                    }%
                  </div>
                  }
                  {columnVisibility.sd_new_to_brand_sales_rate &&
                  <div className="flex-shrink-0 w-[200px] px-4">
                    {
                      ad.sd_sales === 0
                        ? '-'
                        : ((parseFloat(ad.sd_new_to_brand_sales) / parseFloat(ad.sd_sales)) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})
                    }%
                  </div>
                  }
                  {columnVisibility.sd_impression_frequency_average &&
                  <div className="flex-shrink-0 w-[200px] px-4">
                    {
                      ad.sd_cumulative_reach === 0
                        ? '-'
                        : (parseInt(ad.sd_impressions) / parseInt(ad.sd_cumulative_reach)).toFixed(4)
                    }
                  </div>
                  }
                  {columnVisibility.sd_viewability_rate &&
                  <div className="flex-shrink-0 w-[150px] px-4">
                    {
                      ad.sd_impressions === 0
                        ? '-'
                        : ((parseInt(ad.sd_impressions_views) / parseInt(ad.sd_impressions)) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})
                    }%
                  </div>
                  }
                  {columnVisibility.sd_new_to_brand_detail_page_view_rate &&
                  <div className="flex-shrink-0 w-[250px] px-4">
                    {
                      ad.sd_impressions === 0
                        ? '-'
                        : ((parseInt(ad.sd_new_to_brand_detail_page_views) / parseInt(ad.sd_impressions)) * 100).toLocaleString(undefined, {maximumFractionDigits: 2, minimumFractionDigits: 2})
                    }%
                  </div>
                  }
                </li>
              )})}
            </ul>
          </div>
        </div>
      ): null
    ,
    muiTableContainerProps: {
      sx: {
        maxHeight: 'calc(100vh - 156px)',
      }
    },
    muiTableHeadCellProps: {
      sx: {
        color: '#9CA3AF',
        '& .MuiTableSortLabel-icon': {
            marginBottom: '25px',
        },
      },
      align: 'right',
    },
    muiTableHeadRowProps: {
      sx: {
        boxShadow: 'none',
      }
    },
    muiTableBodyRowProps: ({ row }) => ({
      onClick: (event) => {
        console.info(event, row.id);
        setSelectedProduct(row.original);
      },
      sx: {
        cursor: 'pointer',
        '&:hover': {
          backgroundColor: 'rgba(243, 244, 246, 0.5)',
        },
        '&:hover > td:after': {
          backgroundColor: 'rgba(243, 244, 246, 0.5)',
        },
        '&.Mui-TableBodyCell-DetailPanel > td.Mui-TableBodyCell-DetailPanel': {
          position: 'relative',
        },
        '&:after': {
          position: 'absolute',
          content: '""',
          width: row.original.asin === selectedProduct?.asin
            ? '4px'
            : '0px',
          height: '100%',
          left: 0,
          top: 0,
          backgroundColor: row.original.asin === selectedProduct?.asin
            ? '#60a5fa'
            : 'white',
        },
        // '&.Mui-TableBodyCell-DetailPanel > td.Mui-TableBodyCell-DetailPanel > .MuiCollapse-vertical': {
        //   position: 'sticky',
        //   left: 0,
        // },
      },
    }),
    muiTableBodyCellProps: {
      sx: {
        color: '#6b7280',
      },
      align: 'right',
    },
  });

  return (
    <div className="relative">
      <div className="w-full border-t border-l border-r border-gray-200 rounded-t-lg overflow-hidden">
        <div className="w-full h-full overflow-scroll">
          <MaterialReactTable table={table} />
        </div>
      </div>
      <Popover className="absolute top-3.5 right-3 flex items-center justify-center z-[5]">
        {({ open }) => (
          <>
            <PopoverButton
              className={cn(
                "group inline-flex items-center justify-center p-1 rounded-md bg-white border border-gray-200 hover:border-gray-300 focus:outline-none shadow-sm"
              )}
            >
              <AdjustmentsHorizontalIcon
                className={cn(
                  "w-4 h-4 group-hover:text-gray-400 transition-colors duration-200 ease-in-out",
                  open ? "text-gray-500" : "text-gray-300"
                )}
              />
            </PopoverButton>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-200"
              enterFrom="opacity-0 translate-y-1"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-in duration-150"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 translate-y-1"
            >
              <PopoverPanel className="absolute right-full top-0 z-10 mr-2 w-[480px] translate-y-0 transform px-4 sm:px-0">
                <div className="overflow-hidden rounded-lg shadow-lg">
                  <div className="relative px-6 py-2 bg-gray-900/90 text-xs text-white font-normal text-left divide-y divide-gray-600">
                    <div className="py-6">
                      <div className="text-sm text-gray-400 font-semibold">Total Performance</div>
                      <div className="mt-3 flex items-center flex-wrap gap-4">
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("units_ordered")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.units_ordered}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.totalMetrics.unitsSold")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("return_quantity")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.return_quantity}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.totalMetrics.unitsReturned")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("return_sales")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.return_sales}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.totalMetrics.salesReturned")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sessions")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sessions}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.totalMetrics.sessions")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("buy_box_percentage")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.buy_box_percentage}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.totalMetrics.buyBoxPercentage")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("tacos")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.tacos}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.tacos")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("profit")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.profit}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.profit")}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="py-6">
                      <div className="flex items-center gap-x-2 text-sm text-gray-400 font-semibold">
                        <div className="py-0.25 px-1.5 bg-gray-600 text-gray-300 text-[10px] rounded-md">SP</div>
                        Ad Performance
                      </div>
                      <div className="mt-3 flex items-center flex-wrap gap-4">
                      <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("cost")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.cost}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.spCost")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("ad_sales")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.ad_sales}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.spSales")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sp_roas")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sp_roas}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.spRoas")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("units_sold_clicks")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.units_sold_clicks}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.adUnitsSold")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("attributed_sales_same_sku")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.attributed_sales_same_sku}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.attributedSalesSameSku")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("units_sold_same_sku")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.units_sold_same_sku}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.unitsSoldSameSku")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("acos")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.acos}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.spAcos")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("impressions")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.impressions}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.spImpressions")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("clicks")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.clicks}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.spClicks")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("click_through_rate")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.click_through_rate}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.spCtr")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("cost_per_click")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.cost_per_click}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          {t("table.content.adMetrics.spAvgCPC")}
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("ad_conv_rate")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.ad_conv_rate}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          {t("table.content.adMetrics.spCvr")}
                        </div>
                      </div>
                    </div>
                    <div className="py-6">
                      <div className="flex items-center gap-x-2 text-sm text-gray-400 font-semibold">
                        <div className="py-0.25 px-1.5 bg-gray-600 text-gray-300 text-[10px] rounded-md">SD</div>
                        Ad Performance
                      </div>
                      <div className="mt-3 flex items-center flex-wrap gap-4">
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_cost")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_cost}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdCost")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_sales")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_sales}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdSales")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_sales_promoted_clicks")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_sales_promoted_clicks}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdSalesPromotedClicks")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_units_sold")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_units_sold}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdUnitsSold")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_clicks")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_clicks}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdClicks")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_impressions")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_impressions}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdImpressions")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_impressions_views")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_impressions_views}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdImpressionsViews")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_cumulative_reach")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_cumulative_reach}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdCumulativeReach")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_detail_page_views")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_detail_page_views}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdDetailPageViews")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_new_to_brand_detail_page_views")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_new_to_brand_detail_page_views}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdNewToBrandDetailPageViews")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_new_to_brand_sales")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_new_to_brand_sales}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdNewToBrandSales")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_roas")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_roas}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdRoas")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_acos")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_acos}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdAcos")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_conv_rate")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_conv_rate}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdConvRate")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_view_click_through_rate")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_view_click_through_rate}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdViewClickThroughRate")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_new_to_brand_sales_rate")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_new_to_brand_sales_rate}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdNewToBrandSalesRate")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_impression_frequency_average")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_impression_frequency_average}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdImpressionFrequencyAverage")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_viewability_rate")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_viewability_rate}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdViewabilityRate")}
                          </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-gray-200 text-xs font-semibold">
                          <div
                            className="flex-shrink-0"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div
                              className="grid items-center justify-center"
                              onClick={() => handleColumnCheckChange("sd_new_to_brand_detail_page_view_rate")}
                            >
                              <input
                                type="checkbox"
                                className="peer row-start-1 col-start-1 appearance-none w-4 h-4 border ring-transparent border-gray-300 hover:border-blue-300 hover:bg-blue-100/50 rounded checked:bg-blue-400 checked:border-blue-400 cursor-pointer"
                                checked={columnVisibility.sd_new_to_brand_detail_page_view_rate}
                              />
                              <Check className="invisible peer-checked:visible row-start-1 col-start-1 stroke-white" />
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {t("table.content.adMetrics.sdNewToBrandDetailPageViewRate")}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </PopoverPanel>
            </Transition>
          </>
        )}
      </Popover>
    </div>
  )
}
