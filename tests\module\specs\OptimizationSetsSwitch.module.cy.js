/// <reference types="cypress" />

import OptimizationSetsPage from '../../pages/OptimizationSetsPage';
import {
  setupOptimizationSetsSwitchMocks,
  setupOptimizationSetsSwitchErrorMocks,
  optimizationSetsSwitchSelectors,
} from '../mock/OptimizationSetsSwitchMock';

class OptimizationSetsSwitchPage extends OptimizationSetsPage {
  constructor() {
    super();
    // Use selectors from mock file
    Object.assign(this, optimizationSetsSwitchSelectors);
  }

  // Navigation methods specific to switch testing
  navigateToTargetSwitches() {
    // Navigate to optimization sets
    cy.visit('/dashboard?tab=ad-portfolio');
    cy.wait('@getListOptimizations');

    // Click on first optimization set
    this.clickFirstItem();

    // Click on first product to open product detail
    cy.get('[data-testid="product-item"]').first().click();

    // Click on first ad group to show target list with switches
    cy.get('[data-testid="ad-group-item"]').first().click();

    return this;
  }

  // Switch component methods
  assertSwitchExists() {
    cy.get(this.switchContainer).should('be.visible');
    return this;
  }

  assertSwitchIsEnabled(index = 0) {
    this.getSwitchInTargetItem(index).should('be.checked');
    return this;
  }

  assertSwitchIsDisabled(index = 0) {
    this.getSwitchInTargetItem(index).should('not.be.checked');
    return this;
  }

  clickSwitch(index = 0) {
    this.getSwitchInTargetItem(index).click({ force: true });
    return this;
  }

  assertSwitchIsLoading(index = 0) {
    this.getSwitchInTargetItem(index).should('be.disabled');
    return this;
  }

  assertSwitchIsNotLoading(index = 0) {
    this.getSwitchInTargetItem(index).should('not.be.disabled');
    return this;
  }

  // Helper methods
  getTargetListItem(index = 0) {
    return cy.get(this.targetListItem).eq(index);
  }

  getSwitchInTargetItem(index = 0) {
    return this.getTargetListItem(index).find(this.switchInput);
  }

  assertTargetListVisible() {
    cy.get(this.targetListItem).should('have.length.greaterThan', 0);
    return this;
  }
}

describe('OptimizationSets - Switch Component Tests', () => {
  const page = new OptimizationSetsSwitchPage();

  beforeEach(() => {
    cy.login();

    // Mock optimization sets API
    cy.intercept('GET', '/api/optimization/list_optimizations*', {
      statusCode: 200,
      body: [
        {
          id: 98,
          account_id: 'ENTITYPHJJN8ZVVUKS',
          marketplace_id: 'A2EUQ1WTGCTBG2',
          optimization_name: 'Test Optimization Set',
          ad_budget_amount: 14112,
          target_products: [
            {
              asin: 'B08N5WRWNW',
              campaigns: [
                {
                  campaign_id: 'campaign_1',
                  campaign_name: 'Test Campaign',
                  campaign_state: 'ENABLED',
                  ad_groups: [
                    {
                      ad_group_id: 'adgroup_1',
                      ad_group_name: 'Test Ad Group',
                      ad_group_state: 'ENABLED',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    }).as('getListOptimizations');

    // Mock member API
    cy.intercept('GET', '/api/member', {
      statusCode: 200,
      body: {
        id: 'mem_1',
        email: '<EMAIL>',
        lwa_accounts: [
          {
            account_id: 'ENTITYPHJJN8ZVVUKS',
            account_type: 'seller',
            marketplaces: [
              {
                marketplace_id: 'A2EUQ1WTGCTBG2',
                marketplace_name: 'Canada',
                subscription_yn: 'Y',
              },
            ],
          },
        ],
      },
    }).as('getMember');

    // Mock targets API
    cy.intercept('GET', '/api/optimization/list_targets*', {
      statusCode: 200,
      body: [
        {
          target_id: 'target_1',
          target: 'wireless headphones',
          target_type: 'KEYWORD',
          state: 'ENABLED',
          negative: false,
          bid: 1.5,
        },
        {
          target_id: 'target_2',
          target: 'B08N5WRWNW',
          target_type: 'PRODUCT',
          state: 'PAUSED',
          negative: false,
          bid: 2.0,
        },
      ],
    }).as('getTargets');

    // Mock change target status API
    cy.intercept('PUT', '/api/optimization/change_status_of_target*', {
      statusCode: 200,
      body: { status: true, message: 'Target status updated' },
    }).as('changeTargetStatus');

    // Mock other required APIs
    cy.intercept('GET', '/api/member/available_profiles*', { body: [] }).as(
      'getAvailableProfiles'
    );
    cy.intercept('GET', '/api/hourlyReport/new_budget_pacing*', {
      body: {},
    }).as('getNewBudgetPacing');
    cy.intercept('GET', '/api/hourlyReport/campaign-budget-usage*', {
      body: {},
    }).as('getCampaignBudgetUsage');
  });

  describe('Hiển thị Switch Components từ OptimizationSets', () => {
    it('Nên hiển thị switch components khi điều hướng từ OptimizationSets', () => {
      page.navigateToTargetSwitches();

      // Kiểm tra target list hiển thị
      page.assertTargetListVisible();

      // Kiểm tra switch components tồn tại
      page.assertSwitchExists();
    });

    it('Nên hiển thị trạng thái đúng cho các targets', () => {
      page.navigateToTargetSwitches();

      // Target đầu tiên (ENABLED) nên có switch bật
      page.assertSwitchIsEnabled(0);

      // Target thứ hai (PAUSED) nên có switch tắt
      page.assertSwitchIsDisabled(1);
    });

    it('Nên hiển thị switch cho cả keyword và product targets', () => {
      page.navigateToTargetSwitches();

      // Kiểm tra target types
      page.getTargetListItem(0).should('contain', 'KEYWORD');
      page.getTargetListItem(1).should('contain', 'PRODUCT');

      // Cả hai đều nên có switch
      page.getSwitchInTargetItem(0).should('exist');
      page.getSwitchInTargetItem(1).should('exist');
    });
  });

  describe('Chức năng Switch trong OptimizationSets Context', () => {
    beforeEach(() => {
      page.navigateToTargetSwitches();
    });

    it('Nên toggle trạng thái target khi click switch', () => {
      // Lấy trạng thái ban đầu của switch đầu tiên
      page.getSwitchInTargetItem(0).then(($switch) => {
        const wasChecked = $switch.is(':checked');

        // Click switch
        page.clickSwitch(0);

        // Kiểm tra API call với trạng thái mong đợi
        cy.wait('@changeTargetStatus').then((interception) => {
          const expectedState = wasChecked ? 'PAUSED' : 'ENABLED';
          expect(interception.request.url).to.include(`state=${expectedState}`);
        });

        // Kiểm tra trạng thái switch đã thay đổi
        if (wasChecked) {
          page.assertSwitchIsDisabled(0);
        } else {
          page.assertSwitchIsEnabled(0);
        }
      });
    });

    it('Nên xử lý multiple switch toggles', () => {
      // Toggle switch đầu tiên
      page.clickSwitch(0);
      cy.wait('@changeTargetStatus');

      // Toggle switch thứ hai
      page.clickSwitch(1);
      cy.wait('@changeTargetStatus');

      // Cả hai API calls đều nên được thực hiện
      cy.get('@changeTargetStatus.all').should('have.length', 2);
    });

    it('Nên hiển thị loading state khi toggle switch', () => {
      // Mock API response chậm
      cy.intercept(
        'PUT',
        '/api/optimization/change_status_of_target*',
        (req) => {
          req.reply((res) => {
            res.delay(1000);
            res.send({ status: true, message: 'Target status updated' });
          });
        }
      ).as('slowChangeTargetStatus');

      // Click switch
      page.clickSwitch(0);

      // Nên bị disable trong lúc loading
      page.assertSwitchIsLoading(0);

      // Đợi API response
      cy.wait('@slowChangeTargetStatus');

      // Nên được enable lại sau khi loading xong
      page.assertSwitchIsNotLoading(0);
    });
  });

  describe('Error Handling trong OptimizationSets', () => {
    beforeEach(() => {
      page.navigateToTargetSwitches();
    });

    it('Nên xử lý API error gracefully', () => {
      // Mock API error
      cy.intercept('PUT', '/api/optimization/change_status_of_target*', {
        statusCode: 500,
        body: { status: false, message: 'Server error' },
      }).as('changeTargetStatusError');

      // Lấy trạng thái ban đầu
      page.getSwitchInTargetItem(0).then(($switch) => {
        const initialState = $switch.is(':checked');

        // Click switch
        page.clickSwitch(0);

        cy.wait('@changeTargetStatusError');

        // Switch nên trở về trạng thái ban đầu khi có lỗi
        if (initialState) {
          page.assertSwitchIsEnabled(0);
        } else {
          page.assertSwitchIsDisabled(0);
        }
      });
    });

    it('Nên xử lý network timeout', () => {
      // Mock network error
      cy.intercept('PUT', '/api/optimization/change_status_of_target*', {
        forceNetworkError: true,
      }).as('networkError');

      page.clickSwitch(0);

      // Nên xử lý network error gracefully
      cy.wait('@networkError');

      // Switch không nên bị stuck trong loading state
      page.assertSwitchIsNotLoading(0);
    });
  });

  describe('Integration với OptimizationSets Workflow', () => {
    it('Nên refresh target list sau khi toggle thành công', () => {
      page.navigateToTargetSwitches();

      // Click switch để thay đổi state
      page.clickSwitch(0);

      cy.wait('@changeTargetStatus');

      // Target list nên refresh và hiển thị trạng thái cập nhật
      cy.wait('@getTargets');
    });

    it('Nên hoạt động với search/filter targets', () => {
      page.navigateToTargetSwitches();

      // Nếu có chức năng search/filter
      cy.get('[data-testid="target-search"]').then(($search) => {
        if ($search.length > 0) {
          cy.wrap($search).type('wireless');

          // Vẫn nên có thể toggle filtered targets
          page.assertSwitchExists();
          page.clickSwitch(0);

          cy.wait('@changeTargetStatus');
        }
      });
    });

    it('Nên hoạt động với các target types khác nhau', () => {
      page.navigateToTargetSwitches();

      // Test switches hoạt động cho cả keyword và product targets
      const targetTypes = ['KEYWORD', 'PRODUCT'];

      targetTypes.forEach((type, index) => {
        page.getTargetListItem(index).should('contain', type);
        page.clickSwitch(index);
        cy.wait('@changeTargetStatus');
      });
    });
  });

  describe('Accessibility trong OptimizationSets', () => {
    beforeEach(() => {
      page.navigateToTargetSwitches();
    });

    it('Switch nên có thể truy cập bằng bàn phím', () => {
      // Focus vào switch đầu tiên
      page.getSwitchInTargetItem(0).focus();

      // Nên có thể focus
      page.getSwitchInTargetItem(0).should('be.focused');

      // Nên có thể toggle bằng phím space
      page.getSwitchInTargetItem(0).type(' ');

      cy.wait('@changeTargetStatus');
    });

    it('Switch nên có các thuộc tính ARIA phù hợp', () => {
      page.getSwitchInTargetItem(0).should('have.attr', 'type', 'checkbox');
      page.getSwitchInTargetItem(0).should('have.attr', 'role', 'switch');
    });
  });
});
